/**
 * Turkish localization strings
 */

export default {
  // FAQ translations
  faq: {
    navigation: {
      backToHome: "Ana Sayfaya Dön",
      helpCenter: "Yardım Merkezi",
    },
    header: {
      title: "Sıkça Sorulan Sorular",
      subtitle: "CoinScout'un özelliklerini ve hizmetlerini hakkında sık sorulan soruların yanıtlarını bulun.",
    },
    support: {
      title: "Aradığınızı bulamıyor musunuz?",
      description: "Destek ekibimiz size yardımcı olmak için burada! Bize ulaşın:",
      email: "<EMAIL>",
    },
    sections: {
      general: {
        title: "Genel Sorular",
        description: "CoinScout hakkında temel bilgiler",
        questions: {
          whatIs: {
            question: "CoinScout nedir?",
            answer: "CoinScout, yatırımcıların gerçek zamanlı metrikler kullanarak projeleri keşfetmesine ve değerlendirmesine yardımcı olan veri odaklı bir kripto para analiz platformudur. Platformumuz üçüncü taraf kaynaklardan veri toplar ve proje temelleri, tokenomik, güvenlik, pazar ve sosyal metrikler hakkında içgörü sağlamak için özel puanlama algoritmaları kullanır.",
          },
          howWorks: {
            question: "CoinScout nasıl çalışır?",
            answer: "CoinScout, güvenilir üçüncü taraf API'lerden (CryptoRank, CoinGecko, CoinMarketCap, CertiK, Lunar Crush gibi) veri toplar ve kripto para projelerini birden fazla kategoride değerlendirir. Tokenomik, fon toplama, güvenlik denetimleri, sosyal medya metrikleri ve daha fazlası temelinde kapsamlı puanlar sağlayarak hızlı ve verimli bir şekilde bilinçli yatırım kararları vermenize yardımcı oluruz.",
          },
          dataTypes: {
            question: "CoinScout projeleri puanlamak için hangi tür veri kullanır?",
            answer: {
              intro: "Aşağıdakiler dahil olmak üzere saygın platformlardan veri topluyoruz:",
              items: [
                "Pazar verileri (örneğin, pazar değeri, işlem hacmi, likidite)",
                "Tokenomik (örneğin, arz, vesting programları, tahsisler)",
                "Sosyal metrikler (örneğin, takipçi sayıları, sosyal duyarlılık)",
                "Güvenlik denetimleri (örneğin, CertiK denetimleri)",
                "Fon toplama ve proje kilometre taşları",
              ],
            },
          },
          whyLimited: {
            question: "CoinScout neden diğer platformlar on binlerce kripto para varken sadece 2.000-3.000 kripto para listeliyor?",
            answer: {
              intro: "CoinScout, kullanıcıları binlerce alakasız coinle bunaltmak yerine sadece gerçek potansiyeli olan projeleri öne çıkarmaya odaklanır. Uzman ekibimiz, düşük kaliteli projeleri filtreleyen karmaşık ve oldukça seçici bir algoritma geliştirmiştir. Temel kriterlerimizden bazıları şunlardır:",
              items: [
                "Pazar değeri 1 milyon doların altında olan coinler listelenmez.",
                "Puanlama eşiğimizin altında puan alan coinler hariç tutulur.",
                "Stablecoin ve wrapped tokenler gibi varlıkların yanı sıra meme coinleri gibi spekülatif varlıklar dahil edilmez.",
              ],
              conclusion: "Şu anda bu kriterleri karşılayan yaklaşık 2.000-3.000 coin bulunmaktadır ve bu sayı pazar koşullarına göre değişebilir.",
            },
          },
          aiDriven: {
            question: "CoinScout tamamen AI tabanlı mı, yoksa başka faktörler de var mı?",
            answer: {
              intro: "CoinScout sadece AI ile çalışmıyor—en doğru kripto yatırım analizini sağlamak için birden fazla gelişmiş teknoloji ve uzman görüşlerini entegre ediyor. Sistemimiz şu temeller üzerine kurulmuştur:",
              items: [
                "Makine Öğrenmesi (ML): Gerçek zamanlı verilere dayalı sürekli gelişen tahmine dayalı modeller.",
                "Veri Analizi: Yüksek potansiyelli projeleri filtrelemek için derin temel ve duyarlılık analizi.",
                "Deneyimli Kripto Uzmanları: İnsan uzmanlığı, AI tabanlı içgörülerin ilgili, doğru ve eyleme geçirilebilir kalmasını sağlar.",
              ],
              conclusion: "AI, ML, veri analitiği ve insan zekasını birleştirerek CoinScout dengeli ve güvenilir bir yatırım değerlendirme sistemi sağlar.",
            },
          },
        },
      },
      technical: {
        title: "Teknik ve Güvenlik",
        description: "Güvenlik ve teknik detaylar",
        questions: {
          dataAccuracy: {
            question: "Verilerin doğruluğunu nasıl sağlıyorsunuz?",
            answer: "Güvenilirlikleri ile bilinen CoinGecko, CoinMarketCap ve CertiK gibi saygın kaynaklardan veri topluyoruz. Veri doğruluğunu sağlamaya çalışırken, üçüncü taraf veri sınırlamaları nedeniyle tutarsızlıklar oluşabilir. Kullanıcıların gerektiğinde verileri çapraz referans almalarını öneririz.",
          },
          securityAudits: {
            question: "CoinScout projeler üzerinde güvenlik denetimleri yapıyor mu?",
            answer: "Hayır, CoinScout doğrudan güvenlik denetimleri yapmaz. Bunun yerine, CertiK, Hacken ve diğerleri gibi köklü denetim firmalarından denetim verilerini toplarız. Platformumuz, bir projenin denetlenip denetlenmediğini ve mevcut olan denetim sonuçlarını yansıtır.",
          },
        },
      },
      scoring: {
        title: "Puanlama ve Metrikler",
        description: "Puanlama sistemimizi anlama",
        questions: {
          marketCapRatio: {
            question: "Pazar Değeri / FDV Oranı nedir ve neden önemlidir?",
            answer: "Pazar Değeri / FDV (Tamamen Seyreltilmiş Değerleme) Oranı, bir projenin mevcut pazar değeri ile tamamen seyreltilmiş değerlemesi arasındaki ilişkiyi gösteren bir metriktir. Yatırımcıların toplam token arzının ne kadarının şu anda dolaşımda olduğunu ve gelecekteki token çıkışlarının ellerindeki payları seyreltip seyreltmeyeceğini anlamalarına yardımcı olur.",
          },
          tokenomicsScore: {
            question: "Platform tokenomik nasıl puanlıyor?",
            answer: "Tokenomik puanları toplam arz, dolaşımdaki arz, vesting programları ve takım/yatırımcı tahsisleri gibi çeşitli faktörlere dayanır. Adil token dağıtımı, daha uzun vesting programları ve sınırlı token enflasyonu olan projeler daha yüksek puan alma eğilimindedir.",
          },
          memecoins: {
            question: "CoinScout neden meme coinleri dahil etmiyor?",
            answer: {
              intro: "Meme coinleri, genellikle ciddi yatırımcılar için uygun olmayan yüksek spekülatif varlıklar olarak sınıflandırılır. İşte neden onları dahil etmiyoruz:",
              items: [
                "İstatistiksel olarak, meme coin yatırımcılarının çoğu para kaybeder, sadece birkaç içeriden kişi ve şanslı tüccar kar elde eder.",
                "Meme coin piyasası kumar benzer şekilde çalışır, erken girenleri kayırırken geç yatırımcıları dezavantajlı duruma sokar.",
                "CoinScout'un deneyimli yatırım ekibi, kullanıcılarımıza yüksek spekülatif varlıkları tanıtmanın etik olmadığına inanıyor.",
              ],
              conclusion: "Bu nedenlerle, meme coinleri güvenilir, uzun vadeli yatırım içgörüleri sağlama misyonumuzla uyumlu değildir.",
            },
          },
        },
      },
      account: {
        title: "Hesap ve Abonelik",
        description: "Hesabınızı yönetme",
        questions: {
          createAccount: {
            question: "CoinScout'ta nasıl hesap oluştururum?",
            answer: "Ana sayfamızdaki \"Kayıt Ol\" düğmesine tıklayarak ücretsiz veya premium hesap açabilirsiniz. Sadece e-posta adresinizi girin, bir şifre seçin ve başlamak için talimatları izleyin.",
          },
          premiumFeatures: {
            question: "Premium aboneliğe neler dahil?",
            answer: {
              intro: "Premium kullanıcılar şunlara erişebilir:",
              items: [
                "Gelişmiş puanlama metrikleri ve verileri",
                "Tokenomik, sosyal metrikler ve pazar aktivitesine daha derin içgörüler",
                "Kullanıcı tercihlerine dayalı kişiselleştirilmiş yatırım raporları",
                "Öncelikli destek ve yeni özelliklere erken erişim",
              ],
            },
          },
        },
      },
    },
  },

  // Coin list related translations
  coinlist: {
    title: "Tüm Coinler",
    search: "Coin ara...",
    filters: {
      title: "Filtre Seçenekleri",
      description:
        "Gelişmiş filtreleme seçenekleriyle görünümünüzü özelleştirin",
      button: "Filtreler",
    },
    viewAll: "Tümünü Gör",
    headers: {
      name: "İsim",
      tokenomics: "Tokenomik",
      security: "Güvenlik",
      social: "Sosyal",
      market: "Pazar",
      insights: "Analiz",
      totalAIScore: "Toplam AI Skoru",
      "7dChange": "7G Skor Dğş",
      coinhealthscore: "Koin Sağlık Puanı",
    },
    tooltips: {
      name: {
        title: "Coin İsmi ve Sembolü",
        description:
          "Borsalarda listelenen kripto paranın resmi adı ve sembolü.",
      },
      tokenomics: {
        title: "Tokenomik Analizi",
        description:
          "Token arz mekanizmalarını, enflasyon risklerini ve vesting yapılarını ölçer.",
      },
      security: {
        title: "Güvenlik Analizi",
        description:
          "Güvenlik denetim sonuçları ve risk değerlendirme metrikleri.",
      },
      social: {
        title: "Sosyal Analiz",
        description:
          "Sosyal medya varlığı, topluluk katılımı ve duygu analizi.",
      },
      market: {
        title: "Pazar Performans Analizi",
        description:
          "İşlem hacmini, likiditeyi ve genel pazar sağlığını ölçer.",
      },
      insights: {
        title: "AI Analiz İçgörüleri",
        description:
          "AI destekli proje analizi, duygu analizi ve tahmin metrikleri.",
      },
      totalScore: {
        title: "Toplam Skor",
        description:
          "Tüm skorlama metriklerinden hesaplanan genel değerlendirme.",
      },
      sevenDayChange: {
        title: "7 Günlük Skor Değişimi",
        description: "Son 7 gündeki skor değişimi.",
      },
    },
  },
  session: {
    expired: {
      title: "Oturum Zamanaşımına Uğradı",
      description: "Lütfen Tekrar Giriş Yapın",
    },
  },
  // Hover card translations
  hoverCard: {
    marketCapRank: "Piyasa Değeri Sıralaması",
    currentPrice: "Güncel Fiyat",
    marketCap: "Piyasa Değeri",
    watchlistCount: "İzleme Listesi Sayısı",
    usersWatching: "kullanıcı takip ediyor",
    loadingText: "Bilgiler yükleniyor...",
    rankLabel: "#",
    "1day": "1G",
    "1week": "1H",
    "1month": "1A",
    "3months": "3A",
    "1year": "1Y",
  },

  // Calculation data display translations
  calculationData: {
    // Metric names
    metrics: {
      m1: "Piyasa Değeri / FDV Oranı",
      m2: "Maksimum Arz",
      m3: "Kod Güvenliği",
      m4: "Topluluk Güveni",
      m5: "Toplam İşlem Hacmi",
      m6: "En İyi CEX Sıralaması",
      m7: "En İyi DEX Sıralaması",
      m8: "CEX Sayısı",
      m9: "DEX Sayısı",
      m11: "Vesting Planı",
      m12: "Emisyon Skoru (1Y)",
      m14: "Risk/Ödül Değerlendirmesi",
      m17: "Temel Sağlık",
      m18: "Yönetişim Gücü",
      m19: "Pazar Istikrarı",
      m20: "Operasyonel Dayanıklılık",
      m23: "Galaxy Skoru",
      m24: "Alt Sıralama",
      m27: "Duygu",
      m28: "Gecko Portföy Sayısı",
      m29: "Bahsedilme Skoru",
      m30: "Sosyal Dominans",
      default: "Metrik Değeri",
    },
    // UI terms
    ui: {
      overview: "Genel Bakış",
      summary: "Özet",
      score: "Skor",
      status: "Durum",
      mechanisms: "Mekanizmalar",
      total: "Toplam",
      yes: "Evet",
      no: "Hayır",
      deflationary: "Deflasyonist",
      redistribution: "Yeniden Dağıtım",
      buyback: "Geri Alım",
      revenue: "Gelir",
      sharing: "Paylaşım",
      totalUseCases: "Toplam Kullanım Alanları",
      totalMechanisms: "Toplam Mekanizmalar",
      burnMechanisms: "Yakma Mekanizmaları",
      buybackMechanisms: "Geri Alım Mekanizmaları",
      stakingMechanisms: "Staking",
      feeSharingMechanisms: "Ücret Paylaşımı",
      rewardMechanisms: "Ödüller",
      revenueBuybacks: "Gelir Geri Alımları",
      burnBuybacks: "Yakma Geri Alımları",
      hasBurnComponent: "Yakma Bileşeni Var",
      positiveMechanisms: "Olumlu",
      negativeMechanisms: "Olumsuz",
      governanceFeatures: "Yönetişim Özellikleri",
      whitepaperExtract: "🤖Projenin whitepaper'ından çıkarılan ilgili bölüm.",
      revenueSharing: "Gelir Paylaşımı",
      features: "Özellikler",
      feeDistribution: "Ücret Dağıtımı",
      passiveIncome: "Pasif Gelir",
      profitSharing: "Kar Paylaşımı",
      stakingRequired: "Staking Gerekli",
      teamStatus: "Takım Durumu",
      totalTeamMembers: "Toplam Takım Üyeleri",
      governance: "Yönetişim",
      tokenVoting: "Token Oylaması",
      decentralized: "Merkeziyetsiz",
      teamAnonymousOrPublic: "Takım Anonim veya Halka Açık",
      daoGovernance: "DAO Yönetişimi",
      tokenUseCase: "Token Kullanım Alanı",
      deflationaryOrInflationary: "Deflasyonist veya Enflasyonist Token",
      tokenRedistribution: "Token Yeniden Dağıtımı",
      buybackMechanism: "Geri Alım Mekanizması",
      calculationData: "Hesaplama Verisi",
      notAvailable: "M.D.",
      seeAll: "Tümünü Gör",
      seeLess: "Daha Az Göster",
    },
  },

  // Compare page translations
  compare: {
    insights: "İçgörüler",
    tokenomics: "Tokenomiks",
    security: "Güvenlik",
    socials: "Sosyal",
    market: "Market",
    title: "Kripto Para Karşılaştırması",
    overview: "Genel Bakış",
    metrics: "Metrikler",
    categorySelection: "Kategori Seçimi",
    gettingStarted: "Başlangıç",
    selectInstruction:
      "Performans metriklerini karşılaştırmak ve en güçlü yatırım seçeneklerini belirlemek için 4 adede kadar kripto para seçin.",
    selectCoin: "Coin seç",
    bestPerformer: "En İyi Performans",
    overallScore: "Genel Skor",
    noCoinsSelected: "Karşılaştırma için coin seçilmedi",
    searching: "Aranıyor...",
    noResults: "Sonuç Bulunamadı",
    addtext: "Arama için en az 2 karakter ekleyin",
    selectCoinsForBest: "En iyi performansı görmek için coin seçin",
    selectCoinsForMetrics: "Metrikleri görüntülemek için coin seçin",
    metric: "Metrik",
    winner: "Kazanan",
    price: "Fiyat",
    marketCap: "Piyasa Değeri",
    overallRating: "Genel Değerlendirme",
    viewDetailedAnalysis: "Detaylı Analizi Görüntüle",
    scoreFor: "için puan",
    vs: "karşı",
    insight: "İçgörü",
    selectionStatus: "seçildi",
    resetSelection: "Seçimi sıfırla",
    max: "Maksimum",
    oneLeft: "1 kaldı",
    noData: "Veri yok",
    noWinner: "Kazanan yok",
  },

  // Pagination strings
  pagination: {
    showing: "Gösterilen",
    of: "toplam",
    rows: "satır",
  },

  // Authentication namespace with colon-separated keys
  "auth:fields.password": "Şifre",
  "auth:forgotPassword": "Şifremi Unuttum?",
  "auth:remember": "30 gün boyunca beni hatırla",
  "auth:signin.securely": "Güvenli giriş yap",
  "auth:signin.loading": "Giriş yapılıyor...",
  "auth:continueWith": "Veya şununla devam et",
  "auth:termsAccept": "",
  "auth:terms.service": "Hizmet Şartları",
  "auth:terms.privacy": "Gizlilik Politikası",
  "auth:terms.and": "ve",
  "auth:resetPassword": "Şifrenizi Sıfırlayın",
  "auth:backToLogin": "Girişe Dön",

  "auth:password.strength": "Şifre Kalitesi",
  "auth:password.strength.weak": "Zayıf",
  "auth:password.strength.good": "İyi",
  "auth:password.strength.strong": "Güçlü",
  "auth:email": "E-posta",
  "auth:password": "Şifre",
  "auth:login": "Giriş Yap",
  "auth:register": "Kayıt Ol",
  "auth:authentication.signInPrompt":
    "Kişiselleştirilmiş özelliklere erişmek, tercihlerinizi kaydetmek ve CoinScout'un tüm yeteneklerini kullanmak için giriş yapın.",
  "auth:authentication.signin": "Giriş Yap",
  "auth:authentication.continueWithEmail": "E-posta ile devam et",
  "auth:authentication.goBack": "Geri dön",
  "auth:login.title": "CoinScout Giriş",
  "auth:register.title": "Hesap Oluştur",

  // Password validation
  "auth:password.criteria.length": "En az 8 karakter",
  "auth:password.criteria.uppercase": "En az bir büyük harf",
  "auth:password.criteria.lowercase": "En az bir küçük harf",
  "auth:password.criteria.number": "En az bir rakam",
  "auth:password.criteria.special": "En az bir özel karakter",

  // Common search placeholders
  "common:searchCoinsAndTokens": "Coin ve Yaklaşan Projeleri Arayın",
  "common:searchCoins": "Coin arayın...",

  // Common auth validation
  "common:auth:validation.username.min":
    "Kullanıcı adı en az 3 karakter olmalı",
  "common:auth:validation.email.invalid": "Geçerli bir e-posta adresi girin",
  "common:auth:validation.password.min": "Şifre en az 8 karakter olmalı",
  "common:auth:validation.password.uppercase": "En az bir büyük harf içermeli",
  "common:auth:validation.password.lowercase": "En az bir küçük harf içermeli",
  "common:auth:validation.password.number": "En az bir rakam içermeli",
  "common:auth:validation.password.special": "En az bir özel karakter içermeli",
  "common:auth:validation.terms": "Hizmet şartlarını kabul etmelisiniz",
  "common:auth:validation.password.match": "Şifreler eşleşmiyor",

  // Register form fields
  "common:auth:username": "Kullanıcı Adı",
  "common:auth:username.placeholder": "Kullanıcı adınızı seçin",
  "common:auth:email.placeholder": "E-posta adresinizi girin",
  "common:auth:email.description":
    "E-posta adresinizi asla kimseyle paylaşmayız",
  "common:auth:password.create": "Şifre oluşturun",
  "common:auth:password.show": "Şifreyi göster",
  "common:auth:password.confirm": "Şifreyi Onayla",
  "common:auth:password.confirm.placeholder": "Şifrenizi onaylayın",

  // Register form additional text
  "auth:terms.agree": "Hizmet Şartları ve Gizlilik Politikasını kabul ediyorum",
  "auth:captcha.protected": "Bu form reCAPTCHA ile korunmaktadır",
  "auth:register.create": "Hesap oluştur",
  "auth:register.haveAccount": "Zaten hesabınız var mı?",
  "auth:register.emailVerification.title": "E-posta Adresinizi Doğrulayın",
  "auth:register.emailVerification.description":
    "E-posta adresinize bir doğrulama bağlantısı gönderdik. Lütfen gelen kutunuzu kontrol edin ve hesabınızı etkinleştirmek için doğrulama bağlantısına tıklayın.",
  "auth:register.emailVerification.checkSpam":
    "E-postayı göremiyorsanız, lütfen spam klasörünüzü kontrol edin.",
  "auth:register.emailVerification.understood": "Anladım",
  "auth:login.noAccount": "Hesabınız yok mu?",
  "auth:sessionexpired": "Oturum Süresi Doldu",
  "auth:sessionexpiredDescription": "Lütfen Tekrar Giriş Yapın",

  // Login toast messages
  "auth:login.error": "Giriş hatası",
  "auth:login.noResponse": "Sunucudan yanıt alınamadı. Lütfen tekrar deneyin.",
  "auth:login.failed": "Giriş başarısız",
  "auth:login.invalidCredentials":
    "Kimlik doğrulama başarısız. Lütfen bilgilerinizi kontrol edin.",
  "auth:login.successful": "Giriş başarılı",
  "auth:login.welcome": "Tekrar hoş geldiniz!",
  "auth:login.genericError":
    "Giriş yapılamadı. Lütfen bilgilerinizi kontrol edin.",
  "auth:login.unknownError": "Giriş sırasında bilinmeyen bir hata oluştu",

  // Registration toast messages
  "auth:registration.error": "Kayıt hatası",
  "auth:registration.noResponse":
    "Sunucudan yanıt alınamadı. Lütfen tekrar deneyin.",
  "auth:registration.failed": "Kayıt başarısız",
  "auth:registration.genericError": "Kayıt başarısız. Lütfen tekrar deneyin.",
  "auth:registration.sessionError":
    "Hesap oluşturuldu ancak oturum açılamadı. Lütfen giriş yapın.",
  "auth:registration.successful": "Kayıt başarılı",
  "auth:registration.emailVerification":
    "Kayıt başarılı. Lütfen hesabınızı doğrulamak için e-postanızı kontrol edin.",
  "auth:registration.couldNotRegister":
    "Kayıt olunamadı. Lütfen tekrar deneyin.",
  "auth:registration.unknownError":
    "Kayıt sırasında bilinmeyen bir hata oluştu",

  // Logout toast messages
  "auth:logout.successful": "Çıkış yapıldı",
  "auth:logout.success": "Başarıyla çıkış yapıldı.",
  "auth:logout.error": "Çıkış hatası",
  "auth:logout.errorDescription":
    "Çıkış sırasında bir hata oluştu. Lütfen tekrar deneyin.",

  // Authentication error messages
  "auth:authentication.error": "Kimlik doğrulama hatası",
  "auth:authentication.sessionError":
    "Oturum açılamadı. Lütfen tekrar deneyin.",
  "auth:notImplemented": "Uygulanmadı",

  // Authentication prompts
  "auth:authentication.required": "Kimlik Doğrulama Gerekli",
  "auth:authentication.required.description":
    "Bu özelliğe erişmek için giriş yapın",
  "auth:authentication.comparison.required": "Kimlik Doğrulama Gerekli",
  "auth:authentication.comparison.description":
    "Karşılaştırma özelliğini kullanmak için girii� yapın",

  // Common namespace with colon-separated keys
  "common:back.home": "Ana Sayfaya Dön",

  // Nav strings
  "nav:home": "Ana Sayfa",
  "nav:coins": "Coinler",
  "nav:idos": "IDO'lar",
  "nav:portfolio": "Portföy",
  "nav:profile": "Profil",
  "nav:feedback": "Geri Bildirim",
  "nav:logout": "Çıkış",
  "nav:login": "Giriş Yap",
  "nav:register": "Kayıt Ol",
  "nav:trending": "Trendler",
  "nav:favorites": "Favoriler",
  "nav:watchlist": "İzleme Listesi",

  // Navigation colon-separated keys for compatibility
  "navigation:pricing": "Fiyatlandırma",
  "navigation:goToApp": "Uygulamaya Git",
  "navigation:Pricing": "Fiyatlandırma",
  "navigation:Documentation": "Dokümantasyon",
  "navigation:goToHomepage": "Ana Sayfaya Git",
  "navigation:coinScoutAlt": "CoinScout - AI Destekli Kripto Analizi",
  "navigation:login": "Giriş Yap",
  "navigation:signUp": "Kayıt Ol",
  "navigation:logout": "Çıkış Yap",
  "navigation:signup": "Kayıt Ol",
  "navigation:membershipManagement": "Üyelik Yönetimi",

  // System colon-separated keys for compatibility
  "system:language.selector.title": "Dil",
  "system:language.selector.label": "Dil Seçin",
  "system:language.selector.available": "Mevcut Diller",
  "system:auth.required":
    "Coin özetini görüntülemek için giriş yapmanız gerekli",
  "system:auth.loginButton": "Giriş Yap",

  // Sidebar namespace
  sidebar: {
    home: "Ana Sayfa",
    coins: "Coinler",
    topMovers: "En Çok Hareket Edenler",
    watchlist: "İzleme Listesi",
    aiPortfolio: "AI Portföyü",
    portfolioCheckup: "Portföy Kontrolü",
    compareCoins: "Coinleri Karşılaştır",
    upcomingIDOs: "Yaklaşan IDO'lar",
    aiAssistant: "AI Asistanı",
    airdrops: "Airdrop'lar",
    gemScout: "Gem Scout",
    soon: "Yakında",
    cryptoRating: "Kripto Derecelendirme",
    cryptoRatingV2: "Kripto Derecelendirme V2",
    idoRating: "IDO Derecelendirme",
    
    // New Sidebarv2 translations
    coin_ratings: "Coin Derecelendirmeleri",
    ido_ratings: "IDO Derecelendirmeleri", 
    portfolio: "Portföy",
    my_watchlist: "İzleme Listem",
    ai_portfolio_audit: "AI Portföy Denetimi",
    ai_portfolio_generator: "AI Portföy Oluşturucu",
    alpha_tools: "Alfa Araçları",
    ai_tools: "AI Araçları",
    ai_assistant: "AI Asistanı",
    ai_research: "AI Araştırma", 
    compare_coins: "Coinleri Karşılaştır",
    top_launchpads: "En İyi Launchpad'ler",
    airdrops_hub: "Airdrop Merkezi",
    coinscout_academy: "CoinScout Akademisi",
    soon: "Yakında",
    lock: "Kilitle",
    unlock: "Kilidi Aç",
  },

  // Highlights - separate string values for each key
  topGainers: "En Çok Yükselenler",
  newListings: "Yeni Listeler",
  upcomingIDOs: "Yaklaşan IDO'lar",
  score: "Skor",

  // Main coinlist translations
  aiPoweredTitle: "AI Destekli ve Veri Odaklı Kripto Temel Derecelendirmeleri",
  highlights: "Öne Çıkanlar",
  nextDataUpdate: "Sonraki Veri Güncellemesi",
  allCoins: "Tüm Coinler",
  coinDetailDescription: "Detaylı analiz için herhangi bir coin'e tıklayın",
  filtersButton: "Filtreler",
  alertsTitle: "Alarmlar",

  // Table column headers
  name: "İsim",
  tokenomics: "Tokenomik",
  security: "Güvenlik",
  social: "Sosyal",
  market: "Piyasa",
  insights: "Öngörüler",
  totalScore: "Toplam Skor",
  sevenDayChange: "7G Skor Dğş",

  // Score rating colon-separated keys for compatibility
  "score:excellent": "Mükemmel",
  "score:positive": "Pozitif",
  "score:average": "Ortalama",
  "score:weak": "Zayıf",
  "score:critical": "Kritik",
  "score:range": "Skor Aralığı",
  // Alerts namespace
  alerts: {
    price: "Fiyat",
    currentPrice: "Mevcut Fiyat",
    noActiveAlerts: "Henüz Aktif Bir Alarmınız Yok",
    createFirstAlert: "İlk Alarmını Oluştur",
    searchCoins: "Coin Ara",
    addText: "Arama yapmak için en az 2 karakter girin.",
    searching: "Aranıyor...",
    noResults: "Sonuç Bulunamadı",
    priceGoesAbove: "Fiyat şunun üzerine çıktığında",
    priceGoesBelow: "Fiyat şunun altına düştüğünde",
    title: "Kripto Alarmları",
    description:
      "Fiyat değişiklikleri veya AI skor güncellemeleri için bildirim alın",
    createNewAlert: "Yeni Alarm Oluştur",
    activeAlerts: "Aktif Alarmlar",
    notifications: "Bildirimler",
    aiScore: "AI Skor",
    coin: "Coin",
    priceAbove: "Fiyat şunun üzerine çıktığında",
    priceBelow: "Fiyat şunun altına düştüğünde",
    aiScoreAbove: "AI skoru belirlediğiniz eşiği aştığında bildirim alın",
    aiScoreBelow:
      "AI skoru belirlediğiniz eşiğin altına düştüğünde bildirim alın",
    priceAboveDesc: "Fiyat belirlediğiniz eşiği aştığında bildirim alın",
    priceBelowDesc:
      "Fiyat belirlediğiniz eşiğin altına düştüğünde bildirim alın",
    selectCoin: "Coin seçin...",
    targetPriceAbove: "Hedef Fiyat Üstü",
    targetPriceBelow: "Hedef Fiyat Altı",
    enterUpperTargetPrice: "Üst hedef fiyatı girin",
    enterLowerTargetPrice: "Alt hedef fiyatı girin",
    notificationType: "Bildirim Türü",
    browserNotification: "Tarayıcı Bildirimi",
    cancel: "İptal",
    save: "Kaydet",
    back: "Geri",
    scoreGoesAbove: "Skor eşiğin üzerine çıkar",
    scoreGoesBelow: "Skor eşiğin altına düşer",
    aiScoreUpperThreshold: "AI Skor Üst Eşiği",
    aiScoreLowerThreshold: "AI Skor Alt Eşiği",
  },

  marketData: {
    priceChange: "Fiyat Değişimi",
    priceMovement: "Fiyat Hareketi",
    marketCap: "Piyasa Değeri",
    fullyDilute: "Tam Seyreltilmiş",
    fdv: "TSV",
    tradeVolume24h: "24s Hacim",
    volumeMarketCapRatio: "Hacim/Piyasa Değeri Oranı",
    marketCapDetails: "Piyasa Değeri Detayları",
    marketCapRank: "Piyasa Değeri Sıralaması",
    currentRank: "Mevcut sıralama",
    updatedHourly: "Birden fazla borsadan saatlik güncellenir",
  },

  // Top Movers namespace
  topMovers: {
    title: "En Çok Hareket Edenler",
    description:
      "Son 7 günde en yüksek skor artışı gösteren kripto paraları görüntüleyin",
    allCoins: "Tüm Coinler",
    clickForAnalysis: "Detaylı analiz için herhangi bir coine tıklayın",
    searchPlaceholder: "Coin ara...",
    filters: "Filtreler",
  },

  // Filters namespace
  filters: {
    title: "Filtre Seçenekleri",
    description: "Gelişmiş filtreleme seçenekleriyle görünümünüzü özelleştirin",
    button: "Filtreler",
    marketCapRange: "Piyasa Değeri Aralığı (Milyon)",
    projectScoreRange: "Proje Skor Aralığı",
    categories: "Kategoriler",
    chains: "Zincirler",
    selectCategories: "Kategorileri seçin",
    selectChains: "Zincirleri seçin",
    listingDate: "Listeleme Tarihi",
    chainEcosystem: "Zincir / Ekosistem",
    moveSliderToAdjust: "Aralığı ayarlamak için kaydırıcıyı hareket ettirin:",
    applyFilters: "Filtreleri Uygula",
    resetFilters: "Filtreleri Sıfırla",
    loading: "Yükleniyor...",
    noCategoriesFound: "Kategori bulunamadı",
    noChainsFound: "Zincir bulunamadı",
    cancel: "İptal",
    loginRequired: "Filtreleri kullanmak için önce giriş yapmanız gerekiyor.",
    subscriptionRequired:
      "Filtreleri kullanmak için en az Basic pakete sahip olmanız gerekiyor. Lütfen abonelik planınızı yükseltin.",
    login: "Giriş Yap",
    upgradePlan: "Paket Yükselt",
  },

  // Newly Listed Coins namespace
  newlyListed: {
    title: "Yeni Listelenen Coinler",
    description: "Yeni listelenen tüm kripto paraları keşfedin",
    cardTitle: "Yeni Listelenen Coinler",
    clickForAnalysis: "Detaylı analiz için herhangi bir coine tıklayın",
    searchPlaceholder: "Coin ara...",
    selectTimeframe: "Zaman dilimi seçin",
    last24Hours: "Son 24 Saat",
    last7Days: "Son 7 Gün",
    last14Days: "Son 14 Gün",
    last30Days: "Son 30 Gün",
    last90Days: "Son 90 Gün",
    loading: "Yeni listelenen coinler yükleniyor...",
    noCoinsFound: "Yeni listelenen coin bulunamadı.",
    filters: "Filtreler",
  },

  // Coin Age namespace
  coinAge: {
    comingSoon: "Yakında",
    comingInDays: "{days} gün sonra",
    listedToday: "Bugün listelendi",
    oneDayAgo: "1 gün önce",
    daysAgo: "{days} gün önce",
  },

  // Watchlist namespace
  watchlist: {
    add: "İzleme Listesine Ekle",
    addTowatchlist: "İzleme Listesine Ekle",
    addToWatchlist: "İzleme Listesine Ekle",
    inWatchlist: "İzleme Listesinde",
    watchers: "Takipçi",
    alerts: "Alarmlar",
    share: "Paylaş",
    advancedView: "Gelişmiş Görünüm",
    enable: "Etkinleştir",
    disable: "Devre Dışı Bırak",
    enableAdvancedView: "Gelişmiş görünümü etkinleştir",
    disableAdvancedView: "Gelişmiş görünümü devre dışı bırak",
    favorites: "Favoriler",
    addToFavorites: "Kripto paraları favorilerinize ekleyin",
    error: "Hata",
    success: "Başarılı",
    noWatchlistSelected: "İzleme listesi seçilmedi",
    removedFromWatchlist: "İzleme listesinden çıkarıldı",
    coinRemovedFromWatchlist: "{coinName} izleme listesinden çıkarıldı",
    failedToRemoveCoin: "Coin izleme listesinden çıkarılamadı",
    upcomingProjectWatchlists: "Yaklaşan Proje İzleme Listeleri",
    coinWatchlists: "Coin İzleme Listeleri",
    newList: "Yeni Liste",
    selectWatchlistForProject:
      "Bu yaklaşan proje için bir izleme listesi seçin",
    selectWatchlistForCoin: "Bu coin için bir izleme listesi seçin",
    manageAllWatchlists: "Tüm izleme listelerini yönet",
    dataStoredInBrowser: "Veriler tarayıcıda saklanır",
    removeFromWatchlist: "İzleme listesinden çıkar",
    removeCoinFromWatchlist: "{coinName} izleme listesinden çıkar",
    title: "İzleme Listeleri",
    description:
      "Favori kripto paralarınızı takip edin ve gerçek zamanlı güncellemeler alın",
    portfolioScorePerformance: "Portföy Skor Performansı",
    createNewWatchlist: "Yeni İzleme Listesi Oluştur",
    coinWatchlistCreated: "Coin İzleme Listesi Oluşturuldu",
    idoWatchlistCreated: "IDO İzleme Listesi Oluşturuldu",
    coinWatchlistDeleted: "Coin İzleme Listesi Silindi",
    idoWatchlistDeleted: "IDO İzleme Listesi Silindi",
    coinWatchlistUpdated: "Coin İzleme Listesi Güncellendi",
    idoWatchlistUpdated: "IDO İzleme Listesi Güncellendi",
    hasBeenCreatedSuccessfully: "başarıyla oluşturuldu",
    hasBeenDeletedSuccessfully: "başarıyla silindi",
    hasBeenUpdatedSuccessfully: "başarıyla güncellendi",
    failedToUpdateWatchlist:
      "İzleme listesi güncellenemedi. Lütfen tekrar deneyin.",
    watchlistNameCannotBeEmpty: "İzleme listesi adı boş olamaz",
    watchlistCreated: "İzleme Listesi Oluşturuldu",
    watchlistCreatedAndCoinAdded:
      "izleme listesi oluşturuldu ve coin başarıyla eklendi.",
    idoWatchlistCreatedAndProjectAdded:
      "IDO izleme listesi oluşturuldu ve proje başarıyla eklendi.",
    noWatchlistSelectedToast: "İzleme listesi seçilmedi",
    rankTitle: "Sıralama",
    rankDescription: "Mevcut sıralama düzenine göre proje sıralaması.",
    watchlistColumnTitle: "İzleme Listesi",
    watchlistColumnDescription:
      "Projelerinizi kişisel izleme listenize ekleyin veya çıkarın.",
    removeFromIdoWatchlist: "IDO izleme listesinden çıkar",
    upcomingFallback: "Yaklaşan",
    myWatchlist: "İzleme Listem",
    createNewIdoWatchlist: "Yeni IDO İzleme Listesi Oluştur",
    createNewWatchlistTitle: "Yeni İzleme Listesi Oluştur",
    createIdoWatchlistDescription:
      "Yaklaşan IDO projelerini takip etmek için yeni bir izleme listesi oluşturun",
    createWatchlistDescription:
      "Favori kripto paralarınızı organize etmek için yeni bir izleme listesi oluşturun",
    createIdoWatchlistButton: "IDO İzleme Listesi Oluştur",
    createWatchlistButton: "İzleme Listesi Oluştur",
    defaultIdoDescription:
      "Takip ettiğim yaklaşan token lansmanlarının koleksiyonu",
    defaultCoinDescription: "Favori kripto paralarımın koleksiyonu",
    untitledWatchlist: "İsimsiz İzleme Listesi",
    enterWatchlistNamePlaceholder: "İzleme listesi adını girin",
    watchlistDescriptionPlaceholder: "Bu izleme listesinin açıklaması",
    editWatchlist: "İzleme Listesini Düzenle",
    deleteWatchlist: "İzleme Listesini Sil",
    confirmDelete: "Bu izleme listesini silmek istediğinizden emin misiniz?",
    confirmDeleteDescription:
      "Bu işlem geri alınamaz. Bu işlem izleme listenizi ve tüm içeriğini kalıcı olarak silecektir.",
    cancel: "İptal",
    delete: "Sil",
    shareDescription: "İzleme listenizi arkadaşlarınız ve toplulukla paylaşın",
    copyLink: "Bağlantıyı Kopyala",
    linkCopied: "Bağlantı panoya kopyalandı!",
    watchlistName: "İzleme Listesi Adı",
    watchlistDescription: "İzleme Listesi Açıklaması (Opsiyonel)",
    enterWatchlistName: "İzleme listesi adını girin",
    enterDescription: "İzleme listeniz için açıklama girin",
    createDescription:
      "Favori kripto paralarınızı organize etmek için yeni bir izleme listesi oluşturun",
    editDescription: "İzleme listesi detaylarınızı güncelleyin",
    create: "Oluştur",
    save: "Değişiklikleri Kaydet",
    addButton: "Ekle",
    added: "Eklendi",
    default: "Varsayılan",
    notCompatible: "Uyumlu değil",
    item: "öğe",
    items: "öğeler",
    searchPlaceholder: "İzleme listesinde coin ara...",
    noCoinsFound: "Bu izleme listesinde coin bulunamadı",
    addFirstCoin: "Başlamak için ilk coininizi ekleyin",
    sortBy: "Sırala",
    viewMode: "Görünüm Modu",
    displayMode: "Gösterim Modu",
    simple: "Basit",
    advanced: "Gelişmiş",
    grid: "Izgara",
    list: "Liste",
    untitled: "İsimsiz İzleme Listesi",
    preview: "Önizleme",
    shareNote:
      "Not: Bu bağlantıyı paylaşarak izleme listenizi herkese açık hale getirirsiniz",
    noWatchlistsAvailable: "İzleme Listesi Mevcut Değil",
    createFirstWatchlistUpcoming: "İlk İzleme Listesi Oluştur - Yaklaşan",
    upcomingProjectsDescription: "Takip ettiğiniz yaklaşan IDO projeleri",
    favoriteCollection: "Favori kripto paralarımın koleksiyonu",
  },

  // Empty state namespace
  empty: {
    title: "İzleme Listeniz Boş",
    description:
      "Takip etmek istediğiniz coinleri ekleyerek izleme listenizi oluşturun",
    addCoins: "Coin Ekle",
    createWatchlist: "İzleme Listesi Oluştur",
    aiPoweredTitle: "AI Destekli ve Veri Odaklı Kripto Derecelendirmeleri",
    trackDescription:
      "Favori kripto paralarınızı takip edin ve gerçek zamanlı güncellemeler alın",
    coins: "Coinler",
    idos: "IDO'lar",
  },

  notifications: {
    title: "Bildirimler",
    unread: "Okunmamış",
    all: "Tümü",
    alertTriggered: "{{coinName}} Alarmı Tetiklendi",
    priceAbove:
      "{{coinName}} fiyatı ${{threshold}} üzerine çıktı: ${{currentPrice}}",
    priceBelow:
      "{{coinName}} fiyatı ${{threshold}} altına düştü: ${{currentPrice}}",
    noNotifications: "Bildirim bulunamadı.",
    noUnreadNotifications: "Okunmamış bildirim yok",
    allCaughtUp: "Bildirimleri aldığınızda burada görünecekler.",
  },

  coinDetail: {
    overallCategoriesMetricsBreakdown: "Genel Kategori Metrik Dağılımları",
    categories: "Kategoriler",
    backToPreviousPage: "Önceki Sayfaya Dön",
    rankPrefix: "Sıralama #",
    nextUnlock: "Sonraki Kilit Açma",
    unlockInDays: "{days} gün içinde kilit açma",
    about: "Hakkında",
    marketStatistics: "Piyasa İstatistikleri",
    tokenSupplyDynamics: "Token Tedarik Dinamikleri",
    maxSupply: "Maksimum Tedarik",
    totalSupply: "Toplam Tedarik",
    circulatingSupply: "Dolaşımdaki Tedarik",
    burned: "Yakılan",
    locked: "Kilitli",
    lockedPercentage: "Kilitli Yüzde",
    supplyRatioNA: "Tedarik Oranı Mevcut Değil",
    allTimeHigh: "Tüm Zamanların Yüksek",
    allTimeLow: "Tüm Zamanların Düşük",
    marketCap: "Piyasa Değeri",
    volume: "Hacim",
    allTimeHighLow: "Tüm Zamanların İstatistikleri",
    fromATH: "En Yüksekten Şimdiye",
    fromATL: "En Düşükten Şimdiye",
    ofMaxSupply: "Maksimum Tedarikten",
    detailedInformation: "Detaylı Bilgi",
    showMore: "Daha Fazla Göster",
    showLess: "Daha Az Göster",
  },

  priceChart: {
    title: "Fiyat Grafiği",
    current: "Mevcut",
    high: "Yüksek",
    low: "Düşük",
    sevenDays: "7 Gün",
    fourteenDays: "14 Gün",
    thirtyDays: "30 Gün",
    ninetyDays: "90 Gün",
    oneYear: "1 Yıl",
    threeYears: "3 Yıl",
    fiveYears: "5 Yıl",
    allTime: "Tüm Zamanlar",
  },

  scoreChart: {
    title: "Skor Grafiği",
    current: "Mevcut",
    high: "Yüksek",
    low: "Düşük",
    oneMonth: "1 Ay",
    threeMonths: "3 Ay",
    sixMonths: "6 Ay",
    oneYear: "1 Yıl",
    threeYears: "3 Yıl",
    fiveYears: "5 Yıl",
  },

  externalLinks: {
    title: "Dış Bağlantılar",
  },

  format: {
    trillion: "Tr",
    billion: "M",
    million: "Mn",
    thousand: "B",
  },
  emissionScore: {
    undetermined: "Belirsiz",
  },
  maxSupply: {
    uncapped: "Sınırsız",
  },

  // Price Changes section
  priceChanges: {
    title: "Fiyat Değişimleri",
    "1d": "1 Gün",
    "1day": "1 Gün",
    "1w": "1 Hafta",
    "1week": "1 Hafta",
    "1m": "1 Ay",
    "1month": "1 Ay",
    "3m": "3 Ay",
    "3months": "3 Ay",
    "6m": "6 Ay",
    "1y": "1 Yıl",
    "1year": "1 Yıl",
  },

  // Tokenomics namespace
  tokenomicsNamespace: {
    metricsBreakdown: "Tokenomik Metrik Dağılımları",
    metricsBreakdownGeneric: "Metrik Dağılımları",
    weight: "Ağırlık",
    requestFeature: "Öneride Bulun",
    reportError: "Hata Bildir",
  },

  // Coin Health Score
  coinHealth: {
    title: "Coin Sağlık Skoru",
    scoreRange: "Skor Aralığı",
    critical: "Kritik",
    average: "Ortalama",
    excellent: "Mükemmel",
    positive: "Pozitif",
  },

  // Score ratings namespace
  scoreRatings: {
    excellent: "Mükemmel",
    positive: "Pozitif",
    average: "Ortalama",
    weak: "Zayıf",
    critical: "Kritik",
  },

  // Methodology namespace
  methodology: {
    whatAreWeScoring: "Neyi Puanlıyoruz?",
    whyIsThisImportant: "Bu Neden Önemli?",
    scoringLevels: "Puanlama Seviyeleri",
  },

  // Homepage namespace
  homepage: {
    // Hero section
    hero: {
      badge: "AI Destekli Kripto Analiz Platformu",
      title: "Gelişmiş AI Destekli Kripto Para Analizi",
      titleHighlight: "Platformu",
      description:
        "Kapsamlı AI destekli platformumuzla kripto para analizinin geleceğini keşfedin. Detaylı görüşler, doğru puanlar ve daha akıllı yatırım kararları için veri odaklı öneriler alın.",
      ctaButton: "Analize Başla",
      stats: {
        cryptocurrencies: "Kripto Paralar",
        analysisMetrics: "Analiz Metrikleri",
        moreAccuracy: "Daha Fazla Doğruluk",
      },
    },

    // Features section
    features: {
      title: "Her Kripto Yatırımcısı için Güçlü Özellikler",
      subtitle:
        "Bilinçli kripto para yatırım kararları vermenize yardımcı olacak kapsamlı araçlar ve AI destekli görüşler.",
      comprehensiveScoring: "Kapsamlı AI puanlama sistemi",
      aiRating: {
        title: "AI Geliştirilmiş Skor Analizi",
        description:
          "Gelişmiş makine öğrenimi algoritmaları, doğru kripto para derecelendirmeleri ve yatırım görüşleri sağlamak için birden fazla veri noktasını analiz eder.",
        bullets: [
          "40+ metrik boyunca gerçek zamanlı AI puanlaması",
          "Makine öğrenimi tahmin modelleri",
        ],
      },
      idoRating: {
        title: "IDO Derecelendirme Sistemi",
        description:
          "AI destekli risk değerlendirmesi ve potansiyel değerlendirmesi ile kapsamlı İlk DEX Arzı analizi.",
        bullets: [
          "Lansmanöncesi proje değerlendirmesi",
          "Takım ve tokenomik analizi",
          "Lansman potansiyeli puanlaması",
        ],
      },
      compareCoins: {
        title: "Gelişmiş Coin Karşılaştırması",
        description:
          "Detaylı metrikler ve AI üretilmiş görüşlerle kripto paraların yan yana karşılaştırılması.",
        bullets: [
          "Çoklu metrik karşılaştırma panosu",
          "AI destekli benzerlik analizi",
          "Risk-getiri değerlendirmesi",
        ],
      },
      portfolioGenerator: {
        title: "Akıllı Portföy Üreticisi",
        description:
          "Risk toleransınıza ve yatırım hedeflerinize göre AI destekli portföy optimizasyonu.",
        bullets: [
          "Otomatik portföy oluşturma",
          "Risk ayarlı tahsisler",
          "Çeşitlendirme optimizasyonu",
        ],
      },
      portfolioAnalysis: {
        title: "Portföy Sağlık Analizi",
        description:
          "Optimizasyon önerileriyle mevcut portföyünüzün kapsamlı analizi.",
        bullets: [
          "Performans takibi",
          "Yeniden dengeleme önerileri",
          "Risk maruziyeti analizi",
        ],
      },
      launchpads: {
        title: "Launchpad Entegrasyonu",
        description:
          "Seçilmiş proje seçimleriyle en iyi kripto para launchpad'lerine doğrudan erişim.",
        bullets: [
          "Doğrulanmış launchpad projeleri",
          "Erken erişim fırsatları",
          "Durum tespiti raporları",
        ],
      },
      aiAssistant: {
        title: "AI Ticaret Asistanı",
        description:
          "Yatırım kararlarınıza rehberlik etmek için gelişmiş AI ile desteklenen akıllı ticaret asistanı.",
        bullets: [
          "Gerçek zamanlı pazar görüşleri",
          "Kişiselleştirilmiş öneriler",
          "Ticaret sinyali analizi",
        ],
      },
      airdropScore: {
        title: "Airdrop Skor Analizi",
        description:
          "AI destekli puanlama ve uygunluk değerlendirmesi ile airdrop fırsatlarını değerlendirin.",
        bullets: [
          "Airdrop potansiyeli puanlaması",
          "Uygunluk gereksinimleri",
          "Geçmiş başarı oranları",
        ],
      },
      gemScout: {
        title: "Gem Scout Keşfi",
        description:
          "Gelişmiş AI desen tanıma ve pazar analizi kullanarak gizli kripto para değerlerini keşfedin.",
        bullets: [
          "Erken aşama proje keşfi",
          "Pazar desen analizi",
          "Büyüme potansiyeli puanlaması",
        ],
      },
    },

    // Badges
    badges: {
      betaTestingLive: "Beta Test Canlı",
      live: "Canlı",
      betaTestingSoon: "Beta Test Yakında",
      comingSoon: "Yakında",
    },

    // Trusted By section
    trustedBy: {
      title: "Sektör Liderleri Tarafından Desteklenen",
      description:
        "Platformumuz, en doğru ve kapsamlı analizi sunmak için önde gelen kripto para veri sağlayıcıları ve blockchain gezginleriyle entegre olur.",
      stats: {
        coinsAnalyzed: "Analiz Edilen Coinler",
        dataPoints: "Veri Noktaları",
        apiCalls: "API Çağrıları",
        dataSources: "Veri Kaynakları",
      },
      features: {
        aiPowered: "AI Destekli Analiz",
        multiLayer: "Çok Katmanlı Doğrulama",
        enterprise: "Kurumsal Seviye",
      },
    },

    // Intelligence section
    intelligence: {
      title: "AI Destekli Zeka",
      subtitle:
        "Gerçek zamanlı görüşler ve tahmin modellemesi ile kripto para analizinde yapay zekanın gücünü deneyimleyin.",
    },

    // Call to Action
    callToAction: {
      primary:
        "Gerçek zamanlı görüşler ve tahmin modellemesi ile kripto para analizinde yapay zekanın gücünü deneyimleyin.",
      secondary: "AI destekli analizimize güvenen binlerce yatırımcıya katılın",
    },

    // Benefits
    benefits: {
      "0": {
        title: "Gerçek Zamanlı Analiz",
      },
      "1": {
        title: "AI Destekli Görüşler",
      },
      "2": {
        title: "Risk Değerlendirmesi",
      },
    },

    // Buttons
    buttons: {
      getStarted: "Başlayın",
      viewAllQuestions: "Tüm Soruları Görüntüle",
    },

    // FAQ section
    faq: {
      title: "Sıkça Sorulan Sorular",
      questions: {
        "0": {
          question:
            "AI destekli kripto para derecelendirmeleri ne kadar doğru?",
          answer:
            "AI modellerimiz, teknik göstergeler, temel analiz, sosyal duyarlılık ve birden fazla kaynaktan pazar verisi dahil olmak üzere 40+ metriği analiz ederek trend tahmininde %85'in üzerinde doğruluk elde eder.",
        },
        "1": {
          question:
            "CoinScout'u diğer kripto analiz platformlarından farklı kılan nedir?",
          answer:
            "CoinScout, gelişmiş AI algoritmalarını birden fazla kaynaktan gerçek zamanlı verilerle birleştirerek, temel fiyat takibinin ötesine geçerek tokenomik, takım değerlendirmesi ve pazar duyarlılığı analizini içeren kapsamlı analiz sağlar.",
        },
        "2": {
          question:
            "Platform hem yeni başlayanlar hem de deneyimli tüccarlar için uygun mu?",
          answer:
            "Evet, platformumuz birden fazla karmaşıklık seviyesi ile tasarlanmıştır. Yeni başlayanlar basitleştirilmiş görünümler ve AI önerilerini kullanabilirken, deneyimli tüccarlar detaylı metriklere, özel analiz araçlarına ve gelişmiş karşılaştırma özelliklerine erişebilir.",
        },
      },
    },

    // Testimonials
    testimonials: {
      title: "Kullanıcılarımız Ne Diyor",
      "0": {
        text: "CoinScout'un AI analizi, başka türlü kaçıracağım karlı fırsatları belirlememe yardımcı oldu. Kapsamlı puanlama sistemi inanılmaz derecede doğru.",
      },
    },
  },

  // Error namespace
  error: {
    somethingWentWrong: "Bir şeyler ters gitti",
    refreshPage: "Sayfayı Yenile",
    goToHome: "Ana Sayfaya Git",
    clearErrorLogs: "Hata Kayıtlarını Temizle",
  },

  // Footer colon-separated keys for compatibility
  "footer:description":
    "Akıllı yatırım kararları için kapsamlı görüşler, derecelendirmeler ve veri odaklı öneriler sağlayan gelişmiş AI destekli kripto para analiz platformu.",
  "footer:allRightsReserved": "Tüm hakları saklıdır.",

  // Common subscription modal translations
  "common:subscriptionModal.title": "Abonelik Gerekli",
  "common:subscriptionModal.defaultMessage":
    "Bu özelliğe erişmek için aboneliğinizi yükseltmeniz gerekiyor.",
  "common:subscriptionModal.currentPlan": "Mevcut Plan",
  "common:subscriptionModal.viewPricingPlans":
    "Fiyatlandırma Planlarını Görüntüle",
  "common:subscriptionModal.upgradeNote":
    "Tüm özelliklerin kilidini açmak için aboneliğinizi yükseltin",
  "footer:categories.product": "Ürün",
  "footer:categories.learn": "Öğren",
  "footer:categories.community": "Topluluk",
  "footer:categories.legal": "Yasal",
  "footer:links.cryptoRatings": "Kripto Derecelendirmeleri",
  "footer:links.idoRatings": "IDO Derecelendirmeleri",
  "footer:links.aiPortfolioStrategist": "AI Portföy Stratejisti",
  "footer:links.aiPortfolioCheckup": "AI Portföy Kontrolü",
  "footer:links.compareCoins": "Coinleri Karşılaştır",
  "footer:links.recentListings": "Son Listelenenler",
  "footer:links.topMovers": "En Çok Hareket Edenler",
  "footer:links.airdropsHub": "Airdrop Merkezi",
  "footer:links.scoutAI": "Scout AI",
  "footer:links.aiAssistant": "AI Asistanı",
  "footer:links.pricing": "Fiyatlandırma",
  "footer:links.academy": "Akademi",
  "footer:links.documentation": "Dokümantasyon",
  "footer:links.roadmap": "Yol Haritası",
  "footer:links.blog": "Blog",
  "footer:links.faq": "SSS",
  "footer:links.forum": "Forum",
  "footer:links.telegram": "Telegram",
  "footer:links.discord": "Discord",
  "footer:links.twitter": "Twitter",
  "footer:links.publicPortfolios": "Herkese Açık Portföyler",
  "footer:links.communityGuidelines": "Topluluk Kuralları",
  "footer:links.userTestimonials": "Kullanıcı Yorumları",
  "footer:links.privacyPolicy": "Gizlilik Politikası",
  "footer:links.termsOfService": "Hizmet Şartları",
  "footer:links.cookiePolicy": "Çerez Politikası",
  "footer:links.disclaimer": "Sorumluluk Reddi",
  "footer:links.advertisingPolicy": "Reklam Politikası",
  "footer:links.careers": "Kariyer",
  "footer:links.soon": "Yakında",

  // Roadmap translations
  roadmap: {
    title: "Geliştirme Yol Haritası",
    subtitle: "İlerlemelerimizi takip edin, geri bildirim paylaşın ve CoinScout'a gelecek yenilikleri görün",
    loading: "Yol Haritası Yükleniyor",
    loadingSubtitle: "En son geliştirme güncellemeleri getiriliyor...",
    error: "Yol Haritası Yüklenirken Hata",
    errorSubtitle: "Yol haritası verileri yüklenemedi",
    tryAgain: "Tekrar Dene",
    showMore: "Daha Fazla Göster",
    showLess: "Daha Az Göster", 
    completed: "Tamamlandı",
    categories: {
      all: "Tüm Kategoriler",
      feature: "Özellik",
      enhancement: "İyileştirme", 
      "bug-fix": "Hata Düzeltme",
      integration: "Entegrasyon"
    },
    status: {
      completed: "Tamamlandı",
      "in-progress": "Devam Ediyor",
      planned: "Planlandı"
    },
    priority: {
      high: "Yüksek",
      medium: "Orta",
      low: "Düşük"
    },
    columns: {
      featuresTitle: "Özellikler ve İyileştirmeler",
      featuresDescription: "Planlanan özellikler ve geliştirmeler",
      bugsTitle: "Hata Düzeltmeleri",
      bugsDescription: "Çözümlenen sorunlar ve hata düzeltmeleri", 
      progressTitle: "Devam Eden Çalışmalar",
      progressDescription: "Şu anda geliştirme aşamasında"
    },
    stats: {
      totalItems: "Toplam Öğe",
      completed: "Tamamlandı",
      inProgress: "Devam Ediyor",
      planned: "Planlandı", 
      communityVotes: "Topluluk Oyları"
    }
  },

  // Upcoming IDO translations
  upcoming: {
    title: "Yaklaşan Token Satışları",
    subtitle: "Lansmanından önce yeni tokenları keşfedin ve değerlendirin",
    search: "Yaklaşan token satışlarını ara...",
    noResults: "Yaklaşan token satışı bulunamadı",
    loading: "Yaklaşan token satışları yükleniyor...",
    error: "Yaklaşan token satışları yüklenirken hata oluştu",
    retryButton: "Tekrar Dene",
    tba: "Açıklanacak",
    rank: "Sıra #{number}",
    saleType: "Satış Türü",
    points: "puan",
    tokenomics: "Tokenomik",
    security: "Güvenlik",
    social: "Sosyal",
    market: "Pazar",
    insights: "Analiz",
    totalAiScore: "Toplam AI Skoru",
    filters: {
      title: "Filtreler",
      description:
        "Yaklaşan token satışlarını çeşitli kriterlere göre filtreleyin",
      projectScore: "Proje Skoru",
      saleType: "Satış Türü",
      category: "Kategori",
      blockchain: "Blokzincir",
      allTypes: "Tüm Türler",
      allCategories: "Tüm Kategoriler",
      allBlockchains: "Tüm Blokzincirler",
      searchCategories: "Kategorileri ara...",
      searchChains: "Blokzincirleri ara...",
      selectDateRange: "Tarih aralığı seçin",
      last24Hours: "Son 24 Saat",
      last7Days: "Son 7 Gün",
      last14Days: "Son 14 Gün",
      last30Days: "Son 30 Gün",
      last90Days: "Son 90 Gün",
      reset: "Filtreleri Sıfırla",
      apply: "Filtreleri Uygula",
    },
    table: {
      name: "İsim",
      date: "IDO Tarihi",
      launchDate: "IDO Tarihi",
      initialCap: "Başlangıç Limitı",
      totalRaised: "Toplam Toplanan",
      score: "Skor",
      actions: "İşlemler",
      imcScore: "Piyasa Değeri",
      fundingScore: "Fonlama",
      launchpadScore: "Launchpad",
      investorScore: "Yatırımcılar",
      socialScore: "Sosyal",
      totalAiScore: "Toplam AI Skoru",
    },
    tooltips: {
      rank: {
        title: "Sıralama",
        description: "Mevcut sıralama düzenine göre proje sıralaması.",
      },
      watchlist: {
        title: "İzleme Listesi",
        description: "Projeleri kişisel izleme listenize ekleyin veya çıkarın.",
      },
      projectName: {
        title: "Proje Adı",
        description: "Kripto para projesinin resmi adı ve sembol kısaltması.",
      },
      launchDate: {
        title: "IDO Tarihi",
        description:
          "İlk DEX Arzı (IDO) ve token lansmanı için planlanan veya onaylanmış tarih.",
      },
      initialMarketCap: {
        title: "Başlangıç Piyasa Değeri",
        description:
          "Başlangıç Piyasa Değeri metriği, token arzı, başlangıç fiyatı ve piyasa koşullarını dikkate alarak token lansmanındaki öngörülen piyasa değerini değerlendirir.",
      },
      funding: {
        title: "Fonlama Analizi",
        description:
          "Fonlama Skoru metriği projenin fonlama geçmişini, yatırımcı kalitesini ve finansal sürdürülebilirliğini analiz eder.",
      },
      financing: {
        title: "Finansman Analizi",
        description:
          "Finansman Skoru metriği projenin finansman geçmişini, yatırımcı kalitesini ve finansal sürdürülebilirliğini analiz eder.",
      },
      launchpad: {
        title: "Launchpad Analizi",
        description:
          "Launchpad Skoru metriği token satışını gerçekleştiren platformun itibarını ve başarı geçmişini değerlendirir.",
      },
      investors: {
        title: "Yatırımcı Analizi",
        description:
          "Yatırımcı Skoru metriği destekleyen yatırımcıların ve girişim sermayesi firmalarının kalitesini ve itibarını değerlendirir.",
      },
      socialMedia: {
        title: "Sosyal Medya Analizi",
        description:
          "Sosyal Skor metriği, projenin sosyal medya varlığındaki takipçi sayıları, etkileşim oranları ve büyüme trendlerini analiz ederek topluluk ilgisini ölçer.",
      },
      coinScoutAiScore: {
        title: "CoinScout AI Skoru",
        description:
          "Tüm puanlama metriklerinden hesaplanan genel derecelendirme.",
      },
      projectDetails: {
        title: "Proje Detayları",
        description:
          "Bu proje için kapsamlı analiz ve detaylı metrikleri görüntüleyin.",
      },
    },
  },

  // Sale type translations
  saleType: {
    IDO: "IDO",
    IEO: "IEO",
    ICO: "ICO",
    SHO: "SHO",
    Seed: "Tohum",
    IGO: "IGO",
    ISO: "ISO",
  },

  // Common translations
  common: {
    searching: "Aranıyor...",
    cancel: "İptal",
    more: "daha fazla",
    selected: "seçili",
    loading: "Yükleniyor...",
    upcomingIdos: "Yaklaşan IDO'lar",
    priceChart: {
      noDataAvailable: "Fiyat verisi mevcut değil",
    },
  },

  // Logs translations (for development)
  logs: {
    usingIdoWatchlists: "IDO izleme listeleri kullanılıyor:",
    fetchInitialDataStarted: "İlk veri çekme başladı",
    rawAPIData: "Ham API verisi:",
    apiDataLength: "API veri uzunluğu:",
    fetchInitialDataCompleted: "İlk veri çekme tamamlandı",
    firstRecordDetails: "İlk kayıt detayları:",
    imageUrlChecks: "Resim URL kontrolleri",
    allProjectsFromAPI: "API'den tüm projeler",
    usingDirectAPI: "Doğrudan API kullanılıyor",
    rawAPIDataFirst5: "Ham API verisi ilk 5:",
    firstRecordSocialScore: "İlk kayıt sosyal skoru:",
    clickedProjectInfo: "Tıklanan proje bilgisi:",
    redirectingWithDirectID: "Doğrudan ID ile yönlendiriliyor:",
  },

  // Empty state translations
  emptyState: {
    noData: "Veri mevcut değil",
    noDescription: "Açıklama mevcut değil",
    noTeamInfo: "Takım bilgisi mevcut değil",
    noFundingInfo: "Fonlama bilgisi mevcut değil",
    noTokenomics: "Tokenomik verisi mevcut değil",
    noPriceAnalysis: "Fiyat analizi mevcut değil",
    noProjectDetails: "Proje detayları mevcut değil",
    noInvestorInfo: "Yatırımcı bilgisi mevcut değil",
  },
  pricingSave: { save: "İndirim" },
  // IDO feature request translations
  ido: {
    // Status translations
    status: {
      excellent: "Mükemmel",
      good: "İyi",
      fair: "Orta",
      poor: "Zayıf",
      bad: "Kötü",
      upcoming: "Yaklaşan",
      new: "Yeni",
      active: "Aktif",
      growing: "Büyüyen",
      mature: "Olgun",
      declining: "Azalan",
    },
    // Common UI elements
    error: "Hata",
    loadingData: "IDO verileri yükleniyor...",
    noDataAvailable: "Veri mevcut değil",
    fetchingData: "API'den veri alınıyor...",
    unexpectedError: "Beklenmedik bir hata oluştu",
    backToList: "Listeye Dön",
    featureRequest: {
      title: "Özellik Talep Et",
      description: "CoinScout'ta görmek istediğiniz özelliği bize söyleyin.",
      enterDescription: "Lütfen özellik önerinizi girin",
      submitted: "Özellik Önerisi Gönderildi!",
      thankYou:
        "Geri bildiriminiz için teşekkür ederiz! Öneriniz ekibimiz tarafından değerlendirilecektir.",
    },
    metrics: {
      idoPrice: "IDO Fiyatı",
      imc: "IMC",
      fdv: "TSV",
      fundsRaised: "Toplanan Fon",
      initialMarketCap: "Başlangıç Piyasa Değeri",
      marketContext: "Piyasa Bağlamı",
      analysis: "IDO Metrik Analizi",
      maxSupply: "Maksimum Arz",
      totalSupply: "Toplam Arz",
      initialCirculating: "Başlangıç Dolaşım",
      basedOn: "{{count}} metrik temelinde",
      breakdown: "IDO Metrik Dağılımı",
    },

    advancedAnalysis: "Gelişmiş Analiz",
    tabs: {
      team: "Takım",
      funding: "Fonlama ve Yatırım",
      tokenomics: "Tokenomiks",
      releases: "Token Yayını",
      analysis: "Fiyat Analizi",
      details: "Proje Detayları",
    },
    funding: {
      insights: "Fonlama İçgörüleri",
      totalRaised: "Toplanan Fon",
      rounds: "Fonlama Turları",
      athRoi: "En Yüksek ROI",
      currentRoi: "Mevcut ROI",
      saleStartDate: "Satış Başlangıç Tarihi",
      saleEndDate: "Satış Bitiş Tarihi",
      valuationGrowth: "Değerleme Büyümesi",
      seedValuation: "Seed Değerlemesi",
      privateSale: "Özel Satış",
      publicSale: "Halka Satış",
    },
    investors: {
      keyInvestors: "Anahtar Yatırımcılar",
      investor: "Yatırımcı",
      type: "Tür",
      amount: "Miktar",
    },
    projectDescription: "Proje Açıklaması",
    whatIs: "nedir?",
    website: "Web Sitesi",
    tokenAllocation: {
      title: "Token Tahsis Matrisi",
      description: "Gelişmiş dağıtım analitiği ve tokenomik dökümü",
      analytics: "Dağıtım Analitiği",
    },
    twitter: "Twitter / X",
    telegram: "Telegram",
    discord: "Discord",
    medium: "Medium",
    linkedin: "LinkedIn",
    reddit: "Reddit",
    github: "GitHub",
    whitepaper: "Beyaz Kağıt",
    explorer: "Blokzincir Keşifçisi",
    uncategorized: "Kategorisiz",
    submitError: "Gönderim Hatası",
    submitErrorDescription:
      "Öneriniz gönderilirken bir hata oluştu. Lütfen daha sonra tekrar deneyin.",
    errorReport: {
      enterDetails: "Lütfen hata detaylarını girin",
      submitted: "Hata Raporu Gönderildi!",
      thankYou:
        "Hata bildiriminiz için teşekkür ederiz. Ekibimiz en kısa sürede inceleyecektir.",
    },
    // Contract information translations
    contract: "Kontrat",
    contractAddress: "Kontrat Adresi",
    tokenType: "Token Türü",
    decimals: "Ondalık",
    copied: "Kopyalandı!",
    contractAddressCopied: "Kontrat adresi panoya kopyalandı",
    contracts: "Kontratlar",
    links: "Bağlantılar",
    copyContractAddress: "Kontrat adresini kopyala",
    noLaunchpadInfo: "Launchpad Bilgisi Yok",
  },

  // Profile page translations
  profile: {
    title: "Profil",
    description: "Hesap ayarlarınızı ve tercihlerinizi yönetin.",
    saveChanges: "Değişiklikleri Kaydet",
    profile: "Profil",
    notifications: "Bildirimler",
    security: "Güvenlik",
    settings: "Ayarlar",
    achievements: "Başarımlar",
    membership: "Üyelik",
    memberSince: "Üyelik Başlangıcı",
    personalInformation: "Kişisel Bilgiler",
    updateDescription:
      "Profilinizi güncelleyin ve kişisel bilgilerinizi yönetin.",
    // Profile tab
    username: "Kullanıcı Adı",
    email: "E-posta",
    emailPlaceholder: "<EMAIL>",
    bio: "Biyografi",
    bioPlaceholder: "Kendiniz hakkında bilgi verin...",
    avatar: "Avatar",
    membershipTier: "Üyelik Seviyesi",
    premium: "Premium",
    pro: "Pro",
    free: "Ücretsiz",
    status: "Durum",
    active: "Aktif",
    lastLogin: "Son giriş",
    never: "Hiçbir zaman",
    verified: "Doğrulanmış",
    resendVerification: "Doğrulama e-postası gönder",
    areYouSure: "Emin misiniz?",

    // Subscription details
    plan: "Plan",
    planStatus: "Plan Durumu",
    started: "Başlangıç",
    expires: "Bitiş",
    choosePlan: "Planı Seç",
    // Account management
    manageSubscription: "Aboneliğinizi şu bölümde yönetin:",
    tab: "sekmesi",
    downloadData: "Verilerinizi İndirin",
    deleteAccount: "Hesabı Sil",
    absolutelySure: "Kesinlikle emin misiniz?",
    deleteWarning:
      "Bu işlem geri alınamaz. Hesabınızı kalıcı olarak silecek ve verilerinizi sunucularımızdan kaldıracaktır.",
  },

  // Subscription Modal
  subscriptionModal: {
    title: "Abonelik Yükseltme Gerekli",
    defaultMessage:
      "Bu özellik için abonelik planınızı yükseltmeniz gerekiyor.",
    currentPlan: "Mevcut Planınız",
    viewPricingPlans: "Abonelik Planlarını Görüntüle",
    upgradeNote:
      "Planınızı istediğiniz zaman değiştirebilir veya iptal edebilirsiniz.",
  },

  // IDO Table translations
  idoTable: {
    name: {
      title: "İsim",
      description: "Projenin adı ve token sembolü.",
    },
    projectName: {
      title: "İsim",
      description: "Projenin adı ve token sembolü.",
    },
    date: {
      title: "IDO Tarihi",
      description: "Initial DEX Offering (IDO) gerçekleştirilecek tarih.",
    },
    idoDate: {
      title: "Tarih",
      description: "Initial DEX Offering (IDO) gerçekleştirilecek tarih.",
    },
    imcScore: {
      title: "IMC Skoru",
      description:
        "Projenin lansmanındaki token değerlemesini değerlendiren Initial Market Cap skoru.",
    },
    fundingScore: {
      title: "Fonlama Skoru",
      description:
        "Fonlama Skoru projenin bağış toplama geçmişini, yatırımcı kalitesini ve finansal desteğini değerlendirir.",
    },
    launchpadScore: {
      title: "Launchpad Skoru",
      description:
        "Launchpad Skoru token satışını gerçekleştiren platformun güvenilirliğini ve geçmiş başarılarını değerlendirir.",
    },
    investorScore: {
      title: "Yatırımcı Skoru",
      description:
        "Yatırımcı Skoru projenin yatırımcılarının ve destekleyici kuruluşlarının güvenilirliğini ve geçmiş başarılarını değerlendiren bir metriktir.",
    },
    socialScore: {
      title: "Sosyal Skor",
      description:
        "Sosyal Skor projenin topluluk katılımını, sosyal medya varlığını ve genel marka farkındalığını değerlendirir.",
    },
    aiScore: {
      title: "AI Skoru",
      description:
        "Tüm bireysel metrikleri ağırlıklı bir genel skorda birleştiren kapsamlı AI destekli değerlendirme.",
    },
    details: {
      title: "Detaylar",
      description:
        "Tüm metrikler, geçmiş veriler ve derinlemesine raporlar dahil olmak üzere projenin kapsamlı analizini görüntüleyin.",
    },
  },

  // Pricing page translations
  pricing: {
    title: "Her yatırımcı için basit fiyatlandırma",
    description:
      "Yatırım stratejinize en uygun planı seçin. Tüm planlar temel analiz özelliklerimize erişim içerir.",
    monthly: "Aylık",
    yearly: "Yıllık",
    mostPopular: "En Popüler",
    selectPlan: "Plan Seç",
    getStarted: "Başla",
    savingsText: "Yıllık faturalama ile tasarruf edin",
    priceDifference: "Fiyat Farkı",
    currentPlan: "Mevcut Planınız",
    whatsIncluded: "Dahil olanlar:",
    save: "%17 Tasarruf",
    choosePlan: "Plan Seç",
    haveQuestions: "Hala sorunuz mu var?",
    planNames: {
      free: "Ücretsiz",
      basic: "Temel",
      advance: "Gelişmiş",
      premium: "Premium",
    },
    descriptions: {
      free: "Sınırlı özelliklerle temel erişim",
      basic: "Kripto yatırımına başlayın",
      advance: "Aktif yatırımcılar için gelişmiş araçlar",
      premium: "Sınırsız kurumsal erişim",
    },
    subtitles: {
      free: "Yeni başlayanlar için mükemmel",
      basic: "Daha fazlasına ihtiyaç duyan yatırımcılar için",
      advance: "Deneyimli yatırımcılar için",
      premium: "Profesyonel yatırımcılar için",
    },
    haveQuestion: "Hala sorularınız mı var?",
    contactSupport: "İletişime Geçin",
  },

  // Support translations
  support: {
    contactSupport: "Destek ile İletişim",
    contactSupportTitle: "Destek ile İletişim",
    contactSupportDescription:
      "Lütfen aşağıdaki formu doldurun, destek ekibimiz en kısa sürede size dönüş yapacak.",
  },

  // Common translations with colon-separated keys for compatibility
  "common:logs.firstRecordSocialScore": "İlk kayıt sosyal skoru:",
  "common:logs.clickedProjectInfo": "Tıklanan proje bilgisi:",
  "common:logs.redirectingWithDirectID": "Doğrudan ID ile yönlendiriliyor:",
  "common:emptyState.noData": "Veri mevcut değil",
  "common:emptyState.noInvestorInfo": "Yatırımcı bilgisi mevcut değil",
  "common:pricing.save": "İndirim",
};
