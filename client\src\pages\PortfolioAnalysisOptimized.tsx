import React, { useState, useMemo, useCallback, memo } from 'react';
import { Helmet } from 'react-helmet';
import { useLocation } from 'wouter';
import { useQuery } from '@tanstack/react-query';

// Core imports only - no heavy libraries
import { Portfolio, PortfolioAsset } from '../lib/types';
import { portfolioService } from '../lib/services/PortfolioService';
import { Button } from '../components/ui/button';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "../components/ui/alert-dialog";
import {
  Download,
  Share2,
  BarChart3,

  BarChart,
  Award,
  Star,
  Flame,
  Gem,
  Zap,
  Bookmark,
  Heart,
  Inbox,
  Layers,
  Shield,
  DollarSign,
  Briefcase,
  Folder,
  Rocket,
  TrendingUp,
  CircleDollarSign,
  Scale,
  Wallet,
  ArrowDownSquare,
  Clock
} from 'lucide-react';
import { Switch } from '../components/ui/switch';
import { cn } from '../lib/utils';

// Static imports - dynamic loading removed to fix import errors
import ShareMechanism from '../components/share/ShareMechanism';
import { ExportPortfolioModal } from '../components/portfolio/ExportPortfolioModal';
import { CreateAlertDialogMinimal } from '../components/alerts/CreateAlertDialogMinimal';
import { CreatePortfolioModal } from '../components/portfolio/CreatePortfolioModal';
import { EditPortfolioModal } from '../components/portfolio/EditPortfolioModal';
import UnifiedPortfolioAssets from '../components/shared/UnifiedPortfolioAssets';
import { PortfolioCombinedChart } from '../components/portfolio/PortfolioCombinedChart';
import { PortfolioAllocationAnalysis } from '../components/shared/PortfolioAllocationAnalysis';

// Preload component on hover
const preloadComponent = (componentLoader: () => Promise<any>) => {
  componentLoader();
};


// Memoized loading component
const LoadingSpinner = memo(() => (
  <div className="w-full min-h-[300px] flex items-center justify-center">
    <div className="w-8 h-8 border-4 border-blue-500 border-t-transparent rounded-full animate-spin" />
  </div>
));

// Memoized icon renderer to prevent re-renders
const PortfolioIcon = memo(({ icon, className }: { icon: string; className: string }) => {
  const iconMap: Record<string, React.ReactNode> = {
    star: <Star className={className} />,
    flame: <Flame className={className} />,
    gem: <Gem className={className} />,
    zap: <Zap className={className} />,
    bookmark: <Bookmark className={className} />,
    heart: <Heart className={className} />,
    award: <Award className={className} />,
    chart: <BarChart3 className={className} />,
    inbox: <Inbox className={className} />,
    layers: <Layers className={className} />,
    share: <Share2 className={className} />,
    shield: <Shield className={className} />,
    dollar: <DollarSign className={className} />,
    briefcase: <Briefcase className={className} />,
    rocket: <Rocket className={className} />
  };

  return <>{iconMap[icon] || <Folder className={className} />}</>;
});

// Memoized theme color getter
const getThemeColor = (theme: string | undefined) => {
  const themeColors: Record<string, string> = {
    default: 'bg-blue-600/80',
    blue: 'bg-blue-600/80',
    green: 'bg-green-600/80',
    purple: 'bg-purple-600/80',
    amber: 'bg-amber-600/80',
    cyan: 'bg-cyan-600/80',
    rose: 'bg-rose-600/80'
  };
  return themeColors[theme || 'default'] || 'bg-blue-600/80';
};

// Memoized header component to prevent re-renders
const PortfolioHeader = memo(({ portfolio }: { portfolio: Portfolio }) => (
  <div className="flex items-center gap-3 bg-slate-800/60 px-4 py-2 rounded-lg border border-slate-700/40 hover:border-slate-600/60 transition-all duration-300" style={{ width: 'fit-content' }}>
    <div className={cn(
      "h-7 w-7 rounded flex items-center justify-center flex-shrink-0",
      getThemeColor(portfolio.theme)
    )}>
      <PortfolioIcon icon={portfolio.icon || 'folder'} className="h-5 w-5 text-white" />
    </div>
    <div className="flex flex-col">
      <span className="text-sm font-semibold text-foreground leading-tight whitespace-nowrap">{portfolio.name}</span>
      {portfolio.description && (
        <span className="text-xs text-muted-foreground leading-tight whitespace-nowrap">
          {portfolio.description}
        </span>
      )}
    </div>
  </div>
));

// Memoized Overall Portfolio Score component
const OverallPortfolioScore = memo(({ score }: { score: number }) => {
  const getScoreStatus = (score: number) => {
    if (score >= 85) return { label: 'Excellent', color: '#00FF85' };
    if (score >= 70) return { label: 'Good', color: '#3B82F6' };
    if (score >= 50) return { label: 'Average', color: '#FFB800' };
    return { label: 'Weak', color: '#FF3B3B' };
  };

  const status = getScoreStatus(score);

  return (
    <div className="bg-slate-900/50 rounded-lg p-4 border border-slate-700/40 hover:border-slate-600/60 transition-all duration-300">
      <div className="flex items-center gap-2 mb-2">
        <Award className="h-4 w-4" style={{ color: status.color }} />
        <p className="text-xs text-muted-foreground">Overall Portfolio</p>
      </div>
      <div className="flex items-baseline gap-2">
        <p className="text-2xl font-bold text-foreground">{score.toFixed(1)}</p>
        <span className="text-xs text-muted-foreground">/100</span>
        <span className="text-xs font-medium" style={{ color: status.color }}>{status.label}</span>
      </div>
      <div className="mt-2 h-1.5 bg-slate-700/50 rounded-full overflow-hidden">
        <div className="h-full rounded-full transition-all duration-500" style={{ width: `${score}%`, backgroundColor: status.color }}></div>
      </div>
    </div>
  );
});

// Performance optimized portfolio analysis component
export default function PortfolioAnalysisOptimized() {
  const [, setLocation] = useLocation();

  // Fetch portfolios list from API
  const { 
    data: portfoliosResponse, 
    isLoading: portfoliosLoading, 
    error: portfoliosError 
  } = useQuery({
    queryKey: ['portfolios'],
    queryFn: () => portfolioService.getPortfolios(),
    staleTime: 5 * 60 * 1000, // 5 minutes
    refetchOnMount: false,
    retry: 1
  });

  console.log('🚀 [Portfolio API] get_portfolios response:', portfoliosResponse);
  console.log('🔍 [Portfolio API Debug] portfoliosResponse structure:', {
    hasResponse: !!portfoliosResponse,
    success: portfoliosResponse?.success,
    hasData: !!portfoliosResponse?.data,
    hasOutput: !!portfoliosResponse?.output,
    dataLength: portfoliosResponse?.data?.length,
    outputLength: portfoliosResponse?.output?.length,
    fullResponse: portfoliosResponse
  });

  // State consolidation to reduce re-renders  
  const [state, setState] = useState({
    selectedPortfolioId: null as string | null, // Will be set when API data loads
    showCharts: false,
    alertDialogOpen: false,
    selectedAlertCoin: "",
    createPortfolioModalOpen: false,
    editPortfolioModalOpen: false,
    exportModalOpen: false,
    portfolioToEdit: null as Portfolio | null,
    deleteConfirmOpen: false,
    assetToDelete: null as PortfolioAsset | null
  });

  // Determine if we're using API data or mock data
  const usingApiData = portfoliosResponse?.success && portfoliosResponse?.output?.length > 0;
  
  // Fetch selected portfolio details from API
  const { 
    data: portfolioResponse, 
    isLoading: portfolioLoading, 
    error: portfolioError 
  } = useQuery({
    queryKey: ['portfolio', state.selectedPortfolioId],
    queryFn: () => {
      if (!state.selectedPortfolioId) {
        throw new Error('No portfolio ID selected');
      }
      console.log('🚀 [Portfolio Query] Fetching portfolio details for ID:', state.selectedPortfolioId);
      return portfolioService.getPortfolio(state.selectedPortfolioId);
    },
    enabled: !!state.selectedPortfolioId && usingApiData, // Only fetch when we have API data and a valid ID
    staleTime: 30 * 1000, // 30 seconds - shorter for testing
    refetchOnMount: false,
    retry: 1
  });

  console.log('🚀 [Portfolio API] get_portfolio response:', portfolioResponse);

  // Batch state updates
  const updateState = useCallback((updates: Partial<typeof state>) => {
    setState(prev => ({ ...prev, ...updates }));
  }, []);

  // Use only API data - no fallback to mock data
  const portfolios = portfoliosResponse?.success && portfoliosResponse?.output ? portfoliosResponse.output : [];
  const currentPortfolio = portfolioResponse?.success ? portfolioResponse?.output : null;

  console.log('🚀 [Portfolio Selector Debug]', {
    portfoliosResponseSuccess: portfoliosResponse?.success,
    portfoliosDataLength: portfoliosResponse?.data?.length,
    portfoliosOutputLength: portfoliosResponse?.output?.length,
    portfoliosForSelector: portfolios,
    selectedPortfolioId: state.selectedPortfolioId,
    actualAPIData: portfoliosResponse?.output
  });

  console.log('🔧 [Current Portfolio Debug]', {
    portfolioResponseSuccess: portfolioResponse?.success,
    currentPortfolio: currentPortfolio,
    hasAssets: currentPortfolio?.assets?.length,
    assetsData: currentPortfolio?.assets
  });

  console.log('🔧 [Portfolio Dropdown Rendering]', {
    portfoliosLength: portfolios.length,
    portfolioNames: portfolios.map((p: any) => ({ id: p.id, name: p.name, icon: p.icon })),
    usingAPIData: usingApiData,
    selectedPortfolioId: state.selectedPortfolioId,
    portfolioExists: portfolios.find((p: any) => p.id === state.selectedPortfolioId) ? true : false
  });

  // Update selected portfolio ID when API data loads or when selection changes
  React.useEffect(() => {
    if (portfoliosResponse?.success && portfoliosResponse?.output?.length > 0) {
      const apiPortfolioId = portfoliosResponse.output[0].id;
      
      // If no portfolio is selected or current selection doesn't exist in API data, select the first one
      const portfolioExists = portfoliosResponse.output.find((p: any) => p.id === state.selectedPortfolioId);
      
      if (!state.selectedPortfolioId || !portfolioExists) {
        console.log('🔄 [Portfolio Selection] Setting selectedPortfolioId to API data:', apiPortfolioId);
        console.log('🔄 [Portfolio Selection] Available portfolios:', portfoliosResponse.output.map((p: any) => ({ id: p.id, name: p.name })));
        setState(prev => ({ ...prev, selectedPortfolioId: apiPortfolioId }));
      }
    }
  }, [portfoliosResponse?.success, portfoliosResponse?.output, state.selectedPortfolioId, portfolios]);

  // Add additional effect to handle portfolio selection changes and force query refetch
  React.useEffect(() => {
    if (state.selectedPortfolioId && usingApiData) {
      console.log('🔄 [Portfolio Selection] Portfolio ID changed, will trigger API call:', state.selectedPortfolioId);
    }
  }, [state.selectedPortfolioId, usingApiData]);

  // Optimized portfolio calculation with deep memoization
  const selectedPortfolio = useMemo(() => {
    // Safety check: return null if no data available
    if (!state.selectedPortfolioId || !portfolios?.length) {
      return null;
    }

    // First priority: Use detailed API portfolio data if available
    if (currentPortfolio && currentPortfolio.assets) {
      console.log('🚀 [Portfolio Selection] Using detailed API portfolio data');
      return {
        ...currentPortfolio,
        // Transform API assets to component format if needed
        assets: currentPortfolio.assets.map((asset: any) => ({
          ...asset,
          // Ensure compatibility with existing PortfolioAsset interface
          value: asset.currentValue,
          price: asset.currentPrice,
          change24h: asset.change24h || 0,
          change7d: asset.change7d || 0,
          aiScore: asset.aiScore || 0,
          // Map additional fields
          profit: asset.profitLoss,
          profitPercentage: asset.profitLossPercentage
        }))
      };
    }

    // Second priority: Use summary API data from portfolios list
    const apiPortfolio = portfolios.find((p: any) => p.id === state.selectedPortfolioId);
    
    if (apiPortfolio) {
      console.log('🚀 [Portfolio Selection] Using summary API portfolio data');
      // Use API data directly - it already has calculated values
      return {
        ...apiPortfolio,
        // Add derived values if not present
        profit: apiPortfolio.totalValue - apiPortfolio.totalInvested,
        // Use API's dayChangePercentage, but calculate dayChange from totalValue
        dayChange: apiPortfolio.totalValue * (apiPortfolio.dayChangePercentage / 100),
        // Empty assets since detailed data not loaded yet  
        assets: []
      };
    }

    // No valid portfolio found
    return null;
  }, [state.selectedPortfolioId, portfolios, currentPortfolio]);

  // Optimized handlers with no console logs
  const handleAssetClick = useCallback((asset: PortfolioAsset) => {
    requestAnimationFrame(() => {
      setLocation(`/coin/${asset.id}`);
    });
  }, [setLocation]);

  const handleCreateAlert = useCallback((asset: PortfolioAsset) => {
    requestAnimationFrame(() => {
      updateState({ selectedAlertCoin: asset.name, alertDialogOpen: true });
    });
  }, [updateState]);

  const handleAddCoin = useCallback(() => {
    // Open the add coin modal by updating the state
    // This will be handled by the UnifiedPortfolioAssets component
    console.log("🚀 handleAddCoin called - opening add coin modal");
  }, []);

  const handleRemoveCoin = useCallback((asset: PortfolioAsset) => {
    // Show confirmation dialog instead of directly deleting
    updateState({
      assetToDelete: asset,
      deleteConfirmOpen: true
    });
  }, [updateState]);

  const confirmRemoveAsset = useCallback(() => {
    if (!selectedPortfolio || !state.assetToDelete) return;

    // Remove the asset from the portfolio
    const updatedAssets = selectedPortfolio.assets.filter((a: PortfolioAsset) => a.id !== state.assetToDelete!.id);

    // Recalculate portfolio totals
    const totalValue = updatedAssets.reduce((sum: number, a: PortfolioAsset) => sum + a.value, 0);
    const totalInvested = updatedAssets.reduce((sum: number, a: PortfolioAsset) => sum + ((a.averagePrice || a.price) * a.amount), 0);
    const profit = totalValue - totalInvested;
    const profitPercentage = totalInvested > 0 ? (profit / totalInvested) * 100 : 0;

    // Calculate 24h change
    const dayChange = updatedAssets.reduce((sum: number, a: PortfolioAsset) => sum + (a.value * (a.change24h / 100)), 0);
    const dayChangePercentage = totalValue > 0 ? (dayChange / totalValue) * 100 : 0;

    const updatedPortfolio = {
      ...selectedPortfolio,
      assets: updatedAssets,
      totalValue,
      totalInvested,
      profit,
      profitPercentage,
      dayChange,
      dayChangePercentage
    };

    // TODO: API integration needed for deleting assets
    console.warn('Asset deletion requires API integration');
    updateState({
      deleteConfirmOpen: false,
      assetToDelete: null
    });
    
    console.log('Would remove asset:', state.assetToDelete.name, 'from portfolio');
  }, [selectedPortfolio, state.assetToDelete, updateState]);

  const handleCreatePortfolio = useCallback((portfolio?: Portfolio) => {
    if (!portfolio) {
      updateState({ createPortfolioModalOpen: true });
    } else {
      // Portfolio was created, update the selected portfolio
      updateState({
        selectedPortfolioId: portfolio.id,
        createPortfolioModalOpen: false
      });
    }
  }, [updateState]);

  const handleEditPortfolio = useCallback((id: string) => {
    const portfolio = portfolios?.find(p => p.id === id);
    if (portfolio) {
      updateState({ portfolioToEdit: portfolio, editPortfolioModalOpen: true });
    }
  }, [portfolios, updateState]);

  const handleSavePortfolio = useCallback((portfolio: Portfolio) => {
    // TODO: API integration needed for saving portfolio
    console.warn('Portfolio save requires API integration');
    updateState({
      editPortfolioModalOpen: false,
      portfolioToEdit: null
    });
  }, [updateState]);

  const handleDeletePortfolio = useCallback((id: string) => {
    // TODO: API integration needed for deleting portfolio
    console.warn('Portfolio deletion requires API integration');
    console.log('Would delete portfolio with ID:', id);
  }, []);

  // Direct portfolio selector rendering - no useMemo to avoid dependency issues
  console.log('🔧 [Portfolio Selector Data]', { 
    portfoliosCount: portfolios.length,
    selectedId: state.selectedPortfolioId,
    portfolioNames: portfolios.map((p: any) => p.name)
  });

  return (
    <>
      <Helmet>
        <title>Portfolio Analysis - CoinScout</title>
        <meta name="description" content="Analyze your cryptocurrency portfolio performance, allocation, and health metrics" />
      </Helmet>

      <div className="container mx-auto px-3 py-4 sm:p-6" data-page="portfolio-analysis">
        {/* Header - Exactly matching other pages */}
        <div className="flex flex-col mb-8">
          <div className="relative pb-6 border-b border-slate-700/40">
            <div className="flex items-center justify-between mb-3">
              <div className="flex items-center gap-3">
                <div className="h-8 w-1.5 bg-primary rounded-full"></div>
                <h1 className="text-2xl sm:text-3xl font-bold tracking-tight text-foreground">
                  Portfolio Analysis
                </h1>
              </div>
              {selectedPortfolio && <PortfolioHeader portfolio={selectedPortfolio} />}
            </div>
            <div className="flex items-center gap-2 ml-4">
              <BarChart3 className="h-4 w-4 text-primary opacity-80" />
              <span className="text-sm text-muted-foreground font-medium">
                Deep insights into your portfolio performance and allocation
              </span>
            </div>
          </div>
        </div>

        {/* Main Content - Conditional rendering based on selectedPortfolio */}
        {!selectedPortfolio ? (
          // Loading state when no portfolio selected
          <div className="space-y-8">
            <div className="bg-slate-800/90 backdrop-blur-sm border border-slate-700/60 rounded-xl shadow-md shadow-slate-900/40 p-8 text-center">
              <div className="animate-pulse">
                <div className="h-6 bg-slate-700 rounded w-48 mx-auto mb-4"></div>
                <div className="h-4 bg-slate-700 rounded w-32 mx-auto"></div>
              </div>
            </div>
          </div>
        ) : (
          // Main content when portfolio is loaded
          <div className="space-y-8">
            {/* Portfolio Summary */}
            <div className="bg-slate-800/90 backdrop-blur-sm border border-slate-700/60 rounded-xl shadow-md shadow-slate-900/40 p-6 hover:shadow-lg hover:border-slate-600/60 transition-all duration-300">
              {/* Financial Metrics with Overall Portfolio Score */}
              <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
              {/* Total Value */}
              <div className="bg-slate-900/50 rounded-lg p-4 border border-slate-700/40 hover:border-slate-600/60 transition-all duration-300">
                <div className="flex items-center gap-2 mb-2">
                  <Wallet className="h-4 w-4 text-blue-400" />
                  <p className="text-xs text-muted-foreground">Total Value</p>
                </div>
                <div className="flex items-baseline gap-2">
                  <p className="text-2xl font-bold text-foreground">
                    ${(selectedPortfolio.totalValue / 1000).toFixed(1)}k
                  </p>
                </div>
              </div>

              {/* Total Invested */}
              <div className="bg-slate-900/50 rounded-lg p-4 border border-slate-700/40 hover:border-slate-600/60 transition-all duration-300">
                <div className="flex items-center gap-2 mb-2">
                  <ArrowDownSquare className="h-4 w-4 text-purple-400" />
                  <p className="text-xs text-muted-foreground">Total Invested</p>
                </div>
                <div className="flex items-baseline gap-2">
                  <p className="text-2xl font-bold text-foreground">
                    ${(selectedPortfolio.totalInvested / 1000).toFixed(1)}k
                  </p>
                </div>
              </div>

              {/* Profit/Loss */}
              <div className="bg-slate-900/50 rounded-lg p-4 border border-slate-700/40 hover:border-slate-600/60 transition-all duration-300">
                <div className="flex items-center gap-2 mb-2">
                  <TrendingUp className={cn("h-4 w-4", selectedPortfolio.profit >= 0 ? "text-green-500" : "text-red-500")} />
                  <p className="text-xs text-muted-foreground">Profit/Loss</p>
                </div>
                <div className="flex items-baseline gap-2">
                  <p className={cn(
                    "text-2xl font-bold",
                    selectedPortfolio.profit >= 0 ? "text-green-500" : "text-red-500"
                  )}>
                    ${(Math.abs(selectedPortfolio.profit) / 1000).toFixed(1)}k
                  </p>
                  <span className={cn(
                    "text-xs font-medium",
                    selectedPortfolio.profit >= 0 ? "text-green-500" : "text-red-500"
                  )}>
                    {selectedPortfolio.profitPercentage.toFixed(1)}%
                  </span>
                </div>
              </div>

              {/* 24h Change */}
              <div className="bg-slate-900/50 rounded-lg p-4 border border-slate-700/40 hover:border-slate-600/60 transition-all duration-300">
                <div className="flex items-center gap-2 mb-2">
                  <Clock className={cn("h-4 w-4", selectedPortfolio.dayChange >= 0 ? "text-green-500" : "text-red-500")} />
                  <p className="text-xs text-muted-foreground">24h Change</p>
                </div>
                <div className="flex items-baseline gap-2">
                  <p className={cn(
                    "text-2xl font-bold",
                    selectedPortfolio.dayChange >= 0 ? "text-green-500" : "text-red-500"
                  )}>
                    ${(Math.abs(selectedPortfolio.dayChange) / 1000).toFixed(1)}k
                  </p>
                  <span className={cn(
                    "text-xs font-medium",
                    selectedPortfolio.dayChange >= 0 ? "text-green-500" : "text-red-500"
                  )}>
                    {selectedPortfolio.dayChangePercentage.toFixed(1)}%
                  </span>
                </div>
              </div>

              {/* Overall Portfolio Score */}
              <OverallPortfolioScore score={selectedPortfolio.overallScore || 64.6} />
            </div>
          </div>



          {/* Portfolio Combined Chart - Full width, shows when charts enabled */}
          <div className={cn(
            "transition-all duration-500 ease-in-out",
            state.showCharts ? "max-h-none opacity-100" : "max-h-0 opacity-0 overflow-hidden"
          )}>
            <PortfolioCombinedChart
              portfolio={selectedPortfolio}
              timeRange="1M"
            />
          </div>

          {/* Portfolio Assets - Full width with enhanced design consistency */}
          <div className="bg-slate-800/90 backdrop-blur-sm border border-slate-700/60 rounded-xl shadow-md shadow-slate-900/40 hover:shadow-lg hover:border-slate-600/60 transition-all duration-300">
            <div className="flex items-center justify-between bg-slate-900/50 border-b border-slate-700/40 p-4">
              <div className="flex items-center gap-3">
                <BarChart3 className="h-4 w-4 text-primary" />
                <h3 className="text-foreground font-medium">Portfolio Assets</h3>
              </div>
              <div className="flex items-center gap-2">
                <div className="flex items-center gap-2 px-3 py-1.5 bg-slate-800/50 rounded-lg border border-slate-700/40 hover:border-slate-600/60 transition-all duration-300">
                  <label htmlFor="show-charts" className="text-muted-foreground text-sm cursor-pointer">
                    Show Charts
                  </label>
                  <Switch
                    id="show-charts"
                    checked={state.showCharts}
                    onCheckedChange={(checked) => updateState({ showCharts: checked })}
                    className={cn(
                      state.showCharts ? "bg-primary" : "bg-slate-700",
                      "data-[state=checked]:bg-primary"
                    )}
                  />
                </div>
                <div className="w-px h-6 bg-slate-700/40"></div>
                <Button
                  variant="outline"
                  size="sm"
                  className="bg-slate-800/60 border-slate-700/40 text-slate-200 hover:bg-primary hover:text-white hover:border-primary transition-colors"
                  onClick={() => updateState({ exportModalOpen: true })}
                  onMouseEnter={() => preloadComponent(() => import('../components/portfolio/ExportPortfolioModal'))}
                >
                  <Download className="h-3 w-3 mr-1" />
                  Export
                </Button>
                {selectedPortfolio && (
                  <ShareMechanism
                    itemId={selectedPortfolio.id}
                    itemType="portfolio"
                    itemName={selectedPortfolio.name}
                    className="inline-flex"
                  >
                  <Button
                    variant="outline"
                    size="sm"
                    className="bg-slate-800/60 border-slate-700/40 text-slate-200 hover:bg-primary hover:text-white hover:border-primary transition-colors"
                  >
                    <Share2 className="h-3 w-3 mr-1" />
                    Share
                  </Button>
                  </ShareMechanism>
                )}
              </div>
            </div>
            <div className="p-6">
              <UnifiedPortfolioAssets
                portfolio={selectedPortfolio}
                portfolios={portfolios}
                variant="detailed"
                onAssetClick={handleAssetClick}
                onCreateAlert={handleCreateAlert}
                onSelectPortfolio={(id) => {
                  console.log('🔄 [Portfolio Selection] User selected portfolio:', id);
                  console.log('🔄 [Portfolio Selection] Available portfolio IDs:', portfolios.map((p: any) => p.id));
                  updateState({ selectedPortfolioId: id });
                }}
                onEditPortfolio={handleEditPortfolio}
                onCreatePortfolio={handleCreatePortfolio}
                onAddCoin={handleAddCoin}
                onRemoveCoin={handleRemoveCoin}
                className="w-full"
                showCharts={state.showCharts}
                hideHeading={true}
              />
            </div>
          </div>

          {/* Full width - Portfolio Allocation with enhanced design consistency */}
          <div className="bg-slate-800/90 backdrop-blur-sm border border-slate-700/60 rounded-xl shadow-md shadow-slate-900/40 hover:shadow-lg hover:border-slate-600/60 transition-all duration-300">
            <div className="flex items-center bg-slate-900/50 border-b border-slate-700/40 p-4">
              <div className="flex items-center gap-3">
                <BarChart3 className="h-4 w-4 text-primary" />
                <h3 className="text-foreground font-medium">Portfolio Allocation Analysis</h3>
              </div>
            </div>
            <PortfolioAllocationAnalysis className="p-6" />
          </div>
            </div>
          )
        }
      </div>

      {/* Modals */}
      {state.alertDialogOpen && (
        <CreateAlertDialogMinimal
          open={state.alertDialogOpen}
          onOpenChange={(open) => updateState({ alertDialogOpen: open })}
          selectedCoin={state.selectedAlertCoin}
        />
      )}

      {state.createPortfolioModalOpen && (
        <CreatePortfolioModal
          isOpen={state.createPortfolioModalOpen}
          onClose={() => updateState({ createPortfolioModalOpen: false })}
          onCreatePortfolio={handleCreatePortfolio}
        />
      )}

      {state.editPortfolioModalOpen && state.portfolioToEdit && (
        <EditPortfolioModal
          isOpen={state.editPortfolioModalOpen}
          onClose={() => updateState({ editPortfolioModalOpen: false, portfolioToEdit: null })}
          portfolio={state.portfolioToEdit}
          onSavePortfolio={handleSavePortfolio}
          onDeletePortfolio={handleDeletePortfolio}
        />
      )}

      {state.exportModalOpen && selectedPortfolio && (
        <ExportPortfolioModal
          isOpen={state.exportModalOpen}
          onClose={() => updateState({ exportModalOpen: false })}
          portfolio={selectedPortfolio}
        />
      )}

      {/* Delete Asset Confirmation Dialog */}
      <AlertDialog
        open={state.deleteConfirmOpen}
        onOpenChange={(open) => updateState({ deleteConfirmOpen: open, assetToDelete: open ? state.assetToDelete : null })}
      >
        <AlertDialogContent className="bg-slate-800/90 backdrop-blur-sm text-white border border-slate-700/60 shadow-md shadow-slate-900/40 max-w-md">
          <AlertDialogHeader>
            <AlertDialogTitle className="text-foreground">Remove Asset from Portfolio</AlertDialogTitle>
            <AlertDialogDescription className="text-muted-foreground">
              Are you sure you want to remove <span className="font-semibold text-foreground">{state.assetToDelete?.name}</span> from your portfolio?
              This action cannot be undone.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel className="bg-slate-800/60 border-slate-700/40 text-slate-200 hover:bg-primary hover:text-white hover:border-primary transition-colors">
              Cancel
            </AlertDialogCancel>
            <AlertDialogAction
              onClick={confirmRemoveAsset}
              className="bg-red-600 hover:bg-red-700 text-white transition-colors"
            >
              Remove Asset
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  );
}