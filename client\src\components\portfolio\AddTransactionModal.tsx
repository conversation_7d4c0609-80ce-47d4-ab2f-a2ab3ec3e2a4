import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Calendar } from '@/components/ui/calendar';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { cn } from '@/lib/utils';
import { format } from 'date-fns';
import { CalendarIcon, Plus, TrendingUp, TrendingDown, ArrowDownRight, ArrowUpRight } from 'lucide-react';
import { PortfolioAsset } from '@/lib/types';
import { CoinLogo } from '@/components/CoinLogo';
import { StandardModalLayout } from '@/components/shared/StandardModalLayout';

interface AddTransactionModalProps {
  isOpen: boolean;
  onClose: () => void;
  asset: PortfolioAsset | null;
  mode?: 'add' | 'edit';
  editingTransaction?: {
    id?: string;
    type: 'buy' | 'sell' | 'transfer-in' | 'transfer-out';
    amount: number;
    price?: number;
    date: string;
    notes?: string;
    fees?: number;
  };
  onAddTransaction?: (transaction: {
    type: 'buy' | 'sell' | 'transfer-in' | 'transfer-out';
    amount: number;
    price: number;
    date: Date;
    notes?: string;
    fees?: number;
  }) => void;
  onSave?: (transaction: {
    type: 'buy' | 'sell' | 'transfer-in' | 'transfer-out';
    amount: number;
    price?: number;
    date: string;
    notes?: string;
    fees?: number;
  }) => void;
}

export function AddTransactionModal({
  isOpen,
  onClose,
  asset,
  mode = 'add',
  editingTransaction,
  onAddTransaction,
  onSave
}: AddTransactionModalProps) {
  const [transactionType, setTransactionType] = useState<'buy' | 'sell' | 'transfer-in' | 'transfer-out'>(
    editingTransaction?.type || 'buy'
  );
  const [amount, setAmount] = useState(editingTransaction?.amount?.toString() || '');
  const [price, setPrice] = useState(editingTransaction?.price?.toString() || '');
  const [date, setDate] = useState<Date | undefined>(
    editingTransaction?.date ? new Date(editingTransaction.date) : new Date()
  );
  const [notes, setNotes] = useState(editingTransaction?.notes || '');
  const [fees, setFees] = useState(editingTransaction?.fees?.toString() || '');
  const [errors, setErrors] = useState<{amount?: string; price?: string}>({});

  if (!asset) return null;

  const handleSubmit = () => {
    const newErrors: {amount?: string; price?: string} = {};
    
    if (!amount || parseFloat(amount) <= 0) {
      newErrors.amount = 'Please enter a valid amount';
    }
    
    if (!price || parseFloat(price) <= 0) {
      newErrors.price = 'Please enter a valid price';
    }
    
    if (Object.keys(newErrors).length > 0) {
      setErrors(newErrors);
      return;
    }
    
    const transactionData = {
      type: transactionType,
      amount: parseFloat(amount),
      price: parseFloat(price),
      date: date ? format(date, 'yyyy-MM-dd') : format(new Date(), 'yyyy-MM-dd'),
      notes: notes.trim() || undefined,
      fees: fees ? parseFloat(fees) : undefined
    };
    
    if (mode === 'edit' && onSave) {
      onSave(transactionData);
    } else if (mode === 'add' && onAddTransaction) {
      onAddTransaction({
        ...transactionData,
        date: date || new Date()
      });
    }
    
    // Reset form only if adding
    if (mode === 'add') {
      setAmount('');
      setPrice('');
      setDate(new Date());
      setNotes('');
      setFees('');
      setErrors({});
    }
    onClose();
  };

  const totalValue = parseFloat(amount || '0') * parseFloat(price || '0');

  return (
    <StandardModalLayout
      isOpen={isOpen}
      onClose={onClose}
      title={`${mode === 'edit' ? 'Edit' : 'Add'} Transaction - ${asset.name}`}
      description={`${mode === 'edit' ? 'Update' : 'Record a new'} transaction for ${asset.symbol}`}
      primaryButtonText={`${mode === 'edit' ? 'Update' : 'Add'} ${
        transactionType === 'buy' ? 'Buy' : 
        transactionType === 'sell' ? 'Sell' :
        transactionType === 'transfer-in' ? 'Transfer In' :
        'Transfer Out'
      } Transaction`}
      secondaryButtonText="Cancel"
      onPrimaryClick={handleSubmit}
      onSecondaryClick={onClose}
      primaryButtonVariant={
        transactionType === 'buy' || transactionType === 'transfer-in' ? 'default' : 
        transactionType === 'sell' ? 'destructive' : 
        'default'
      }
      isPrimaryButtonDisabled={!amount || !price || parseFloat(amount) <= 0 || parseFloat(price) <= 0}
      maxWidth="md"
    >
        <div className="space-y-4">
          {/* Transaction Type */}
          <div className="bg-slate-800/40 rounded-lg p-4 space-y-3 border border-slate-700/40">
            <Label className="text-sm font-medium text-slate-300">Transaction Type</Label>
            <Select value={transactionType} onValueChange={(value: 'buy' | 'sell' | 'transfer-in' | 'transfer-out') => setTransactionType(value)}>
              <SelectTrigger className="w-full bg-slate-800/60 border-slate-700/40 text-slate-200 hover:bg-primary hover:text-white hover:border-primary transition-colors">
                <SelectValue />
              </SelectTrigger>
              <SelectContent className="bg-slate-900 border-slate-700">
                <SelectItem value="buy">
                  <div className="flex items-center gap-2">
                    <TrendingUp className="h-4 w-4 text-green-400" />
                    Buy
                  </div>
                </SelectItem>
                <SelectItem value="sell">
                  <div className="flex items-center gap-2">
                    <TrendingDown className="h-4 w-4 text-red-400" />
                    Sell
                  </div>
                </SelectItem>
                <SelectItem value="transfer-in">
                  <div className="flex items-center gap-2">
                    <ArrowDownRight className="h-4 w-4 text-blue-400" />
                    Transfer In
                  </div>
                </SelectItem>
                <SelectItem value="transfer-out">
                  <div className="flex items-center gap-2">
                    <ArrowUpRight className="h-4 w-4 text-orange-400" />
                    Transfer Out
                  </div>
                </SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* Amount and Price */}
          <div className="bg-slate-800/40 rounded-lg p-4 space-y-3 border border-slate-700/40">
            <div>
              <Label className="text-sm font-medium text-slate-300">Amount ({asset.symbol})</Label>
              <Input
                type="number"
                placeholder="0.00"
                value={amount}
                onChange={(e) => {
                  setAmount(e.target.value);
                  setErrors(prev => ({ ...prev, amount: undefined }));
                }}
                className={cn(
                  "bg-slate-800/60 border-slate-700/40 text-slate-200 placeholder:text-slate-500 focus:border-primary focus:ring-primary",
                  errors.amount && "border-red-500 focus:border-red-500"
                )}
              />
              {errors.amount && (
                <p className="text-red-400 text-sm mt-1">{errors.amount}</p>
              )}
            </div>

            <div>
              <Label className="text-sm font-medium text-slate-300">Price per {asset.symbol} (USD)</Label>
              <Input
                type="number"
                placeholder="0.00"
                value={price}
                onChange={(e) => {
                  setPrice(e.target.value);
                  setErrors(prev => ({ ...prev, price: undefined }));
                }}
                className={cn(
                  "bg-slate-800/60 border-slate-700/40 text-slate-200 placeholder:text-slate-500 focus:border-primary focus:ring-primary",
                  errors.price && "border-red-500 focus:border-red-500"
                )}
              />
              {errors.price && (
                <p className="text-red-400 text-sm mt-1">{errors.price}</p>
              )}
            </div>
          </div>

          {/* Total Value */}
          {totalValue > 0 && (
            <div className="bg-blue-900/20 border border-blue-700/30 rounded-lg p-3">
              <div className="flex justify-between items-center">
                <span className="text-blue-300 text-sm">Total Value</span>
                <span className="text-white font-medium">
                  ${totalValue.toFixed(2)}
                </span>
              </div>
            </div>
          )}

          {/* Date and Notes */}
          <div className="bg-slate-800/40 rounded-lg p-4 space-y-3 border border-slate-700/40">
            <div>
              <Label className="text-sm font-medium text-slate-300">Transaction Date</Label>
              <Popover>
                <PopoverTrigger asChild>
                  <Button
                    variant="outline"
                    className={cn(
                      "w-full justify-start text-left font-normal bg-slate-800/60 border-slate-700/40 hover:bg-primary hover:text-white hover:border-primary transition-colors",
                      !date && "text-slate-500"
                    )}
                  >
                    <CalendarIcon className="mr-2 h-4 w-4" />
                    {date ? format(date, 'PPP') : <span>Pick a date</span>}
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0 bg-slate-900 border-slate-700">
                  <Calendar
                    mode="single"
                    selected={date}
                    onSelect={setDate}
                    initialFocus
                    className="bg-slate-900"
                  />
                </PopoverContent>
              </Popover>
            </div>

            <div>
              <Label className="text-sm font-medium text-slate-300">Notes (Optional)</Label>
              <Input
                placeholder="Add any notes about this transaction"
                value={notes}
                onChange={(e) => setNotes(e.target.value)}
                className="bg-slate-800/60 border-slate-700/40 text-slate-200 placeholder:text-slate-500 focus:border-primary focus:ring-primary"
              />
            </div>

            <div>
              <Label className="text-sm font-medium text-slate-300">Transaction Fees (Optional)</Label>
              <Input
                type="number"
                placeholder="0.00"
                value={fees}
                onChange={(e) => setFees(e.target.value)}
                className="bg-slate-800/60 border-slate-700/40 text-slate-200 placeholder:text-slate-500 focus:border-primary focus:ring-primary"
              />
            </div>
          </div>
        </div>
    </StandardModalLayout>
  );
}