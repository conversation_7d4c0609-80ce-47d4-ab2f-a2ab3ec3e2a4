import React from 'react';
import { Card, CardContent } from "@/components/ui/card";
import { motion } from "framer-motion";
import { PortfolioSuggestion, RiskProfile } from '@/lib/types';
import { ResponsiveContainer, AreaChart, Area, XAxis, YAxis, CartesianGrid, Tooltip } from 'recharts';
import { SectorDiversificationScore } from './SectorDiversificationScore';
import { PortfolioCompositionChart } from './PortfolioCompositionChart';
import { PortfolioHealthScore } from './PortfolioHealthScore';
import { CoinScoutProjectScore } from './CoinScoutProjectScore';
import { StablecoinRatioMetric } from './StablecoinRatioMetric';
import { InvestmentBalanceMetric } from './InvestmentBalanceMetric';
import { TrendingSectorsScore } from './TrendingSectorsScore';
import { MarketCapMetric } from './MarketCapMetric';
import { cn } from '@/lib/utils';

export function PortfolioAnalysisComponent({ 
  analysis, 
  suggestion, 
  riskProfile,
  showRebalancing = true,
  scoreContributions,
  isLoading,
  portfolio
}: PortfolioAnalysisProps) {
  const [hoveredValue, setHoveredValue] = React.useState('');

  return (
    <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
      <div className="space-y-6">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.2 }}
          className="w-full"
          data-section="healthOverview"
        >
          <Card className="w-full bg-[#132F4C]/25 hover:bg-[#132F4C]/25 border-[#1E4976] shadow-none">
            <CardContent className="p-6 space-y-6">
              {portfolio ? (
                <PortfolioHealthScore
                  scoreContributions={scoreContributions}
                  totalScore={portfolio.overallScore || portfolio.metrics?.totalScore || 0}
                  isLoading={isLoading}
                  className="w-full"
                />
              ) : (
                <div className="h-[200px] flex items-center justify-center">
                  <div className="text-center space-y-3">
                    <div className="w-12 h-12 border-4 border-[#00E4FF] border-t-transparent rounded-full animate-spin mx-auto" />
                    <div className="text-slate-400">Loading portfolio health score...</div>
                  </div>
                </div>
              )}

              {portfolio ? (
                <div className="space-y-8 mt-6">
                  <div className="space-y-8">
                    <CoinScoutProjectScore
                      averageScore={portfolio.metrics?.projectQuality?.score || 87.5}
                      score={portfolio.metrics?.projectQuality?.score || 0}
                    />

                    <StablecoinRatioMetric
                      stablecoinPercentage={portfolio.metrics?.stablecoinRatio?.percentage || 15}
                      score={portfolio.metrics?.stablecoinRatio?.score || 0}
                    />

                    <InvestmentBalanceMetric
                      largestAssetPercentage={portfolio.metrics?.investmentBalance?.percentage || 25}
                      score={portfolio.metrics?.investmentBalance?.score || 0}
                    />
                  </div>

                  <div className="space-y-8">
                    <SectorDiversificationScore
                      score={portfolio.metrics?.diversification?.score || portfolio.metrics?.sectorBalance?.score || 0}
                      sectors={[
                        { name: 'DeFi', percentage: 30 },
                        { name: 'Gaming', percentage: 25 },
                        { name: 'Layer 1', percentage: 20 },
                        { name: 'Infrastructure', percentage: 15 },
                        { name: 'Others', percentage: 10 }
                      ]}
                      sectorCount={5}
                    />

                    <TrendingSectorsScore
                      score={portfolio.metrics?.sectorBalance?.score || 0}
                      sectors={[
                        { name: 'AI', percentage: 20, growth: 15 },
                        { name: 'DeFi 2.0', percentage: 15, growth: 12 },
                        { name: 'GameFi', percentage: 10, growth: 8 }
                      ]}
                      totalAllocation={45}
                    />

                    <MarketCapMetric
                      percentage={35}
                      score={portfolio.metrics?.marketCapDistribution?.score || 0}
                      largeCapPercentage={35}
                      midCapPercentage={25}
                      smallCapPercentage={7}
                      largeCapScore={95}
                      midCapScore={85}
                      smallCapScore={90}
                    />
                  </div>
                </div>
              ) : (
                <div className="h-[400px] flex items-center justify-center">
                  <div className="text-center space-y-3">
                    <div className="w-12 h-12 border-4 border-[#00E4FF] border-t-transparent rounded-full animate-spin mx-auto" />
                    <div className="text-slate-400">Loading portfolio metrics...</div>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        </motion.div>
      </div>
    </div>
  );
}

interface PortfolioAnalysisProps {
  analysis?: any;
  suggestion?: PortfolioSuggestion;
  riskProfile?: RiskProfile['type'];
  showRebalancing?: boolean;
  scoreContributions?: any; 
  isLoading: boolean;
  portfolio?: any; // Portfolio can be null during loading
}