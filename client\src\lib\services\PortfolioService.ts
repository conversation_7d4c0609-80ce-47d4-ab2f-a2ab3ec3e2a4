/**
 * Portfolio Service - API Integration Layer
 * Handles all portfolio-related API calls
 */

import { 
  ApiResponse, 
  PortfolioListApiModel, 
  PortfolioApiModel,
  CreatePortfolioRequest,
  UpdatePortfolioRequest,
  AddAssetRequest,
  UpdateAssetRequest,
  AddTransactionRequest,
  TransactionApiModel,
  PortfolioFiltersRequest,
  ApiErrorResponse 
} from '../../../../shared/portfolio-api-models';

import api from '../api';

class PortfolioService {
  private baseUrl = '/api/portfolios';

  /**
   * Get all portfolios for the authenticated user
   */
  async getPortfolios(filters?: PortfolioFiltersRequest): Promise<any> {
    try {
      console.log('🚀 [PortfolioService] Getting portfolios list...');
      
      const payload: any = {
        f: "get_portfolios"
      };

      // Add filter parameters if provided
      if (filters) {
        if (filters.theme) payload.theme = filters.theme;
        if (filters.minValue) payload.minValue = filters.minValue;
        if (filters.maxValue) payload.maxValue = filters.maxValue;
        if (filters.minScore) payload.minScore = filters.minScore;
        if (filters.maxScore) payload.maxScore = filters.maxScore;
        if (filters.sortBy) payload.sortBy = filters.sortBy;
        if (filters.sortOrder) payload.sortOrder = filters.sortOrder;
        if (filters.isPublic !== undefined) payload.isPublic = filters.isPublic;
      }

      console.log('📤 [PortfolioService] Request payload:', payload);
      
      const response = await api.post("/client.php", payload);
      
      console.log('✅ [PortfolioService] Portfolios response:', response.data);
      return response.data;

    } catch (error: any) {
      console.error('❌ [PortfolioService] Error fetching portfolios:', error);
      throw this.handleApiError(error);
    }
  }

  /**
   * Get detailed portfolio information by ID
   */
  async getPortfolio(id: string): Promise<any> {
    try {
      console.log(`🚀 [PortfolioService] Getting portfolio details for ID: ${id}`);
      
      const payload = {
        f: "get_portfolio",
        id: id
      };

      console.log('📤 [PortfolioService] Request payload:', payload);
      
      const response = await api.post("/client.php", payload);
      
      console.log('✅ [PortfolioService] Portfolio details response:', response.data);
      return response.data;

    } catch (error: any) {
      console.error(`❌ [PortfolioService] Error fetching portfolio ${id}:`, error);
      throw this.handleApiError(error);
    }
  }

  /**
   * Create a new portfolio
   */
  async createPortfolio(data: CreatePortfolioRequest): Promise<any> {
    try {
      console.log('🚀 [PortfolioService] Creating new portfolio:', data.name);
      
      const payload = {
        f: "create_portfolio",
        name: data.name,
        description: data.description || '',
        icon: data.icon || 'star',
        theme: data.theme || 'default',
        isPublic: data.isPublic || false
      };

      console.log('📤 [PortfolioService] Create Portfolio Request payload:', payload);
      
      const response = await api.post("/client.php", payload);
      
      console.log('✅ [PortfolioService] Portfolio created successfully:', response.data);
      return response.data;

    } catch (error: any) {
      console.error('❌ [PortfolioService] Error creating portfolio:', error);
      throw this.handleApiError(error);
    }
  }

  /**
   * Update an existing portfolio
   */
  async updatePortfolio(id: string, data: UpdatePortfolioRequest): Promise<ApiResponse<PortfolioApiModel>> {
    try {
      console.log(`🚀 [PortfolioService] Updating portfolio ${id}`);
      
      const response = await api.put<ApiResponse<PortfolioApiModel>>(`${this.baseUrl}/${id}`, data);
      console.log('✅ [PortfolioService] Portfolio updated:', response.data?.data?.name);
      return response.data;

    } catch (error: any) {
      console.error(`❌ [PortfolioService] Error updating portfolio ${id}:`, error);
      throw this.handleApiError(error);
    }
  }

  /**
   * Delete a portfolio
   */
  async deletePortfolio(id: string): Promise<ApiResponse<{ success: boolean }>> {
    try {
      console.log(`🚀 [PortfolioService] Deleting portfolio ${id}`);
      
      const response = await api.delete<ApiResponse<{ success: boolean }>>(`${this.baseUrl}/${id}`);
      console.log('✅ [PortfolioService] Portfolio deleted');
      return response.data;

    } catch (error: any) {
      console.error(`❌ [PortfolioService] Error deleting portfolio ${id}:`, error);
      throw this.handleApiError(error);
    }
  }

  /**
   * Add an asset to a portfolio
   */
  async addAsset(portfolioId: string, data: AddAssetRequest): Promise<any> {
    try {
      console.log(`🚀 [PortfolioService] Adding asset to portfolio ${portfolioId}:`, data.coinId);
      
      const payload = {
        f: "add_asset",
        portfolioId: portfolioId,
        coinId: data.coinId,
        amount: data.amount,
        averageCost: data.averageCost,
        // Optional fields
        ...(data.note && { note: data.note })
      };

      console.log('📤 [PortfolioService] Add Asset Request payload:', payload);
      
      const response = await api.post("/client.php", payload);

      console.log('✅ [PortfolioService] Asset added successfully:', response.data);
      return response.data;

    } catch (error: any) {
      console.error(`❌ [PortfolioService] Error adding asset to portfolio ${portfolioId}:`, error);
      throw this.handleApiError(error);
    }
  }

  /**
   * Update an existing asset in a portfolio
   */
  async updateAsset(portfolioId: string, assetId: string, data: UpdateAssetRequest): Promise<ApiResponse<any>> {
    try {
      console.log(`🚀 [PortfolioService] Updating asset ${assetId} in portfolio ${portfolioId}`);
      
      const response = await api.put(`${this.baseUrl}/${portfolioId}/assets/${assetId}`, data);
      console.log('✅ [PortfolioService] Asset updated successfully');
      return response.data;

    } catch (error: any) {
      console.error(`❌ [PortfolioService] Error updating asset ${assetId}:`, error);
      throw this.handleApiError(error);
    }
  }

  /**
   * Remove an asset from a portfolio
   */
  async removeAsset(portfolioId: string, assetId: string): Promise<ApiResponse<{ success: boolean }>> {
    try {
      console.log(`🚀 [PortfolioService] Removing asset ${assetId} from portfolio ${portfolioId}`);
      
      const response = await api.delete<ApiResponse<{ success: boolean }>>(`${this.baseUrl}/${portfolioId}/assets/${assetId}`);
      console.log('✅ [PortfolioService] Asset removed successfully');
      return response.data;

    } catch (error: any) {
      console.error(`❌ [PortfolioService] Error removing asset ${assetId}:`, error);
      throw this.handleApiError(error);
    }
  }

  /**
   * Add a transaction to a portfolio asset
   */
  async addTransaction(portfolioId: string, data: AddTransactionRequest): Promise<any> {
    try {
      console.log(`🚀 [PortfolioService] Adding transaction to portfolio ${portfolioId}:`, {
        assetId: data.assetId,
        type: data.type,
        amount: data.amount
      });
      
      // Convert transfer types from kebab-case to snake_case for API compatibility
      const apiTransactionType = data.type === 'transfer-in' ? 'transfer_in' : 
                                data.type === 'transfer-out' ? 'transfer_out' : 
                                data.type;
      
      const payload = {
        f: "add_transaction",
        portfolioId: portfolioId,
        assetId: data.assetId,
        type: apiTransactionType,
        amount: data.amount,
        price: data.price,
        // Optional fields
        ...(data.fees && { fees: data.fees }),
        ...(data.notes && { notes: data.notes }),
        ...(data.executedAt && { executedAt: data.executedAt })
      };

      console.log('📤 [PortfolioService] Add Transaction Request payload:', payload);
      
      const response = await api.post("/client.php", payload);

      console.log('✅ [PortfolioService] Transaction added successfully:', response.data);
      return response.data;

    } catch (error: any) {
      console.error(`❌ [PortfolioService] Error adding transaction to portfolio ${portfolioId}:`, error);
      throw this.handleApiError(error);
    }
  }

  /**
   * Refresh portfolio prices (get latest market data)
   */
  async refreshPortfolioPrices(id: string): Promise<ApiResponse<PortfolioApiModel>> {
    try {
      console.log(`🚀 [PortfolioService] Refreshing prices for portfolio ${id}`);
      
      const response = await api.post<ApiResponse<PortfolioApiModel>>(`${this.baseUrl}/${id}/refresh-prices`);
      console.log('✅ [PortfolioService] Portfolio prices refreshed');
      return response.data;

    } catch (error: any) {
      console.error(`❌ [PortfolioService] Error refreshing portfolio prices ${id}:`, error);
      throw this.handleApiError(error);
    }
  }

  /**
   * Get portfolio metrics and analytics
   */
  async getPortfolioMetrics(id: string): Promise<ApiResponse<any>> {
    try {
      console.log(`🚀 [PortfolioService] Getting metrics for portfolio ${id}`);
      
      const response = await api.get<ApiResponse<any>>(`${this.baseUrl}/${id}/metrics`);
      console.log('✅ [PortfolioService] Portfolio metrics fetched');
      return response.data;

    } catch (error: any) {
      console.error(`❌ [PortfolioService] Error fetching portfolio metrics ${id}:`, error);
      throw this.handleApiError(error);
    }
  }

  /**
   * Get AI-powered portfolio suggestions
   */
  async getPortfolioSuggestions(id: string): Promise<ApiResponse<any>> {
    try {
      console.log(`🚀 [PortfolioService] Getting AI suggestions for portfolio ${id}`);
      
      const response = await api.get<ApiResponse<any>>(`${this.baseUrl}/${id}/suggestions`);
      console.log('✅ [PortfolioService] Portfolio suggestions fetched');
      return response.data;

    } catch (error: any) {
      console.error(`❌ [PortfolioService] Error fetching portfolio suggestions ${id}:`, error);
      throw this.handleApiError(error);
    }
  }

  /**
   * Handle API errors and provide meaningful error messages
   */
  private handleApiError(error: any): Error {
    if (error.response?.data?.error) {
      const apiError = error.response.data as ApiErrorResponse;
      return new Error(`API Error: ${apiError.error.message}`);
    }
    
    if (error.response?.status === 401) {
      return new Error('Unauthorized access. Please login again.');
    }
    
    if (error.response?.status === 403) {
      return new Error('Access denied. You don\'t have permission to perform this action.');
    }
    
    if (error.response?.status === 404) {
      return new Error('Portfolio not found.');
    }
    
    if (error.response?.status >= 500) {
      return new Error('Server error. Please try again later.');
    }
    
    return new Error(error.message || 'An unexpected error occurred');
  }
}

// Create and export a singleton instance
export const portfolioService = new PortfolioService();
export default portfolioService;