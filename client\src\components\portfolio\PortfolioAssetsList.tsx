import React, { useState, useMemo, useRef, useLayoutEffect, useEffect } from 'react';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from "@/components/ui/table";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuGroup,
  DropdownMenuLabel,
  DropdownMenuSeparator
} from "@/components/ui/dropdown-menu";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { AiScoreBadge } from "@/components/AiScoreBadge";
import { ScoreIndicator } from "@/components/ScoreIndicator";
import { ScoreGauge } from "@/components/ScoreGauge";
import { ScoreBreakdownTooltip } from "../ScoreBreakdownTooltip";
import { CoinLogo } from "@/components/CoinLogo";
import {
  BarChart3,
  ArrowUpRight,
  ArrowDownRight,
  MoreHorizontal,
  Search,
  XCircle,
  SlidersHorizontal,
  Bell,
  ExternalLink,
  Eye,
  ArrowUpDown,
  Pencil,
  PlusCircle,
  FolderPlus,
  Folder,
  ChevronDown,
  ChevronRight,
  Trash2,
  Check,
  FolderCog,
  PencilLine,
  ArrowLeftRight,
  History,
  RefreshCw,
  GripVertical,
  ChevronUp,
  Plus,
  X,
  Clock
} from 'lucide-react';
import { Portfolio, PortfolioAsset } from '@/lib/types';
import { cn } from '@/lib/utils';
import { TableLayoutModal } from '@/components/shared/TableLayoutModal';
import { portfolioService } from '@/lib/services/PortfolioService';
import { CoinStatus } from '@/types/CoinStatus';
import { AddTransactionModal } from './AddTransactionModal';
import { CreatePortfolioModal } from './CreatePortfolioModal';
import { TransactionHistoryModal } from './TransactionHistoryModal';
import { AssetDetailModal } from './AssetDetailModal';
import { ExportPortfolioModal } from './ExportPortfolioModal';
import ShareMechanism from '@/components/share/ShareMechanism';
import { CreateAlertDialogMinimal } from '@/components/alerts/CreateAlertDialogMinimal';
import { AddCoinModal } from './AddCoinModal';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { AnimatePresence, motion } from 'framer-motion';
import {
  Star, Flame, Gem, Zap, Bookmark, Heart, Award,
  Inbox, Layers, Share2, Shield, DollarSign, Briefcase, Rocket
} from 'lucide-react';
import {
  DndContext,
  closestCenter,
  KeyboardSensor,
  PointerSensor,
  useSensor,
  useSensors,
  DragEndEvent,
} from '@dnd-kit/core';
import {
  arrayMove,
  SortableContext,
  sortableKeyboardCoordinates,
  verticalListSortingStrategy,
  useSortable,
} from '@dnd-kit/sortable';
import {
  CSS,
} from '@dnd-kit/utilities';

// Column configuration type
interface ColumnConfig {
  key: string;
  label: string;
  category: 'core' | 'price' | 'market' | 'tokenomics' | 'fundraising' | 'scores';
  enabled: boolean;
  width?: string;
}



// Default column configuration - merging CoinList columns with Portfolio-specific columns
const DEFAULT_COLUMNS: ColumnConfig[] = [
  // Portfolio-specific columns (always visible as core columns)
  { key: 'asset', label: 'Asset', category: 'core', enabled: true, width: '200px' }, // Asset name + symbol
  { key: 'holdings', label: 'Holdings', category: 'core', enabled: true, width: '140px' },
  { key: 'allocation', label: 'Allocation', category: 'core', enabled: true, width: '140px' },
  { key: 'value', label: 'Value', category: 'core', enabled: true, width: '140px' },
  { key: 'profitLoss', label: 'Profit/Loss', category: 'core', enabled: true, width: '140px' },

  // Core columns from CoinList
  { key: 'price', label: 'Price', category: 'market', enabled: true, width: '140px' },
  { key: 'marketCap', label: 'Market Cap', category: 'market', enabled: false, width: '140px' },
  { key: 'age', label: 'Age', category: 'market', enabled: false, width: '120px' },

  // Price change columns
  { key: 'priceChange1h', label: '1h %', category: 'price', enabled: false, width: '120px' },
  { key: 'priceChange24h', label: '24h %', category: 'price', enabled: true, width: '120px' },
  { key: 'priceChange7d', label: '7d %', category: 'price', enabled: false, width: '120px' },
  { key: 'priceChange30d', label: '30d %', category: 'price', enabled: false, width: '120px' },
  { key: 'priceChange60d', label: '60d %', category: 'price', enabled: false, width: '120px' },
  { key: 'priceChange180d', label: '180d %', category: 'price', enabled: false, width: '120px' },
  { key: 'priceChange1y', label: '1y %', category: 'price', enabled: false, width: '120px' },

  // Market data columns
  { key: 'volume24h', label: '24h Volume', category: 'market', enabled: false, width: '140px' },
  { key: 'category', label: 'Category', category: 'market', enabled: false, width: '140px' },
  { key: 'listingDate', label: 'Listing Date', category: 'market', enabled: false, width: '140px' },
  { key: 'fdv', label: 'FDV', category: 'market', enabled: false, width: '140px' },
  { key: 'marketCapToFdv', label: 'MC/FDV', category: 'market', enabled: false, width: '120px' },

  // Tokenomics columns
  { key: 'circSupply', label: 'Circ. Supply', category: 'tokenomics', enabled: false, width: '140px' },
  { key: 'circSupplyPercent', label: 'Circ. Supply (%)', category: 'tokenomics', enabled: false, width: '140px' },
  { key: 'totalSupply', label: 'Total Supply', category: 'tokenomics', enabled: false, width: '140px' },

  // Fundraising columns
  { key: 'fundraise', label: 'Fundraise', category: 'fundraising', enabled: false, width: '140px' },
  { key: 'icoPrice', label: 'ICO Price', category: 'fundraising', enabled: false, width: '140px' },
  { key: 'raise', label: 'Raise', category: 'fundraising', enabled: false, width: '140px' },
  { key: 'icoDate', label: 'ICO Date', category: 'fundraising', enabled: false, width: '140px' },

  // Score columns
  { key: 'security', label: 'Security', category: 'scores', enabled: false, width: '130px' },
  { key: 'social', label: 'Social', category: 'scores', enabled: false, width: '130px' },
  { key: 'market', label: 'Market', category: 'scores', enabled: false, width: '130px' },
  { key: 'tokenomics', label: 'Tokenomics', category: 'scores', enabled: false, width: '130px' },
  { key: 'insights', label: 'Insights', category: 'scores', enabled: false, width: '130px' },
  { key: 'totalScore', label: 'Total AI Score', category: 'scores', enabled: true, width: '140px' },
  { key: 'sevenDayChange', label: '7D Score', category: 'scores', enabled: false, width: '140px' },
];

export interface PortfolioAssetsListProps {
  portfolio: Portfolio;
  portfolios?: Portfolio[];
  variant?: 'default' | 'compact' | 'detailed';
  onAssetClick?: (asset: PortfolioAsset) => void;
  onCreateAlert?: (asset: PortfolioAsset) => void;
  onSelectPortfolio?: (id: string) => void;
  onEditPortfolio?: (id: string) => void;
  onCreatePortfolio?: () => void;
  onAddCoin?: () => void;
  onRemoveCoin?: (asset: PortfolioAsset) => void;
  className?: string;
  // Show/hide charts in the assets list
  showCharts?: boolean;
  // Hide the heading when embedded in containers that already have a heading
  hideHeading?: boolean;
  // Callback for when showCharts changes
  onShowChartsChange?: (showCharts: boolean) => void;
}

type SortField = 'allocation' | 'value' | 'price' | 'change24h' | 'change1h' | 'change7d' | 'change30d' | 'change60d' | 'aiScore' | 'profit';
type SortDirection = 'asc' | 'desc';

// Categories for Portfolio table
const portfolioCategories = {
  core: 'Portfolio Columns',
  price: 'Price Changes',
  market: 'Market Data',
  tokenomics: 'Tokenomics',
  fundraising: 'Fundraising',
  scores: 'AI Scores'
};

export function PortfolioAssetsList({
  portfolio,
  portfolios = [],
  variant = 'default',
  onAssetClick,
  onCreateAlert,
  onSelectPortfolio,
  onEditPortfolio,
  onCreatePortfolio,
  onAddCoin,
  onRemoveCoin,
  className,
  // Show/hide charts in the table
  showCharts = true,
  // Hide the heading when embedded in containers that already have a heading
  hideHeading = false,
  // Callback for showCharts changes
  onShowChartsChange
}: PortfolioAssetsListProps) {
  const [searchQuery, setSearchQuery] = useState('');
  const [sortField, setSortField] = useState<SortField>('allocation');
  const [sortDirection, setSortDirection] = useState<SortDirection>('desc');
  const [transactionModalOpen, setTransactionModalOpen] = useState(false);
  const [transactionHistoryModalOpen, setTransactionHistoryModalOpen] = useState(false);

  const [createPortfolioModalOpen, setCreatePortfolioModalOpen] = useState(false);
  const [showLayoutDialog, setShowLayoutDialog] = useState(false);
  const [columns, setColumns] = useState<ColumnConfig[]>(DEFAULT_COLUMNS);
  const [selectedAsset, setSelectedAsset] = useState<PortfolioAsset | null>(null);
  const [showAllAssets, setShowAllAssets] = useState(false);

  // Additional state for recent modal integrations
  const [selectedAssetForDetail, setSelectedAssetForDetail] = useState<PortfolioAsset | null>(null);
  const [isAssetDetailOpen, setIsAssetDetailOpen] = useState(false);
  const [selectedAssetForAddTransaction, setSelectedAssetForAddTransaction] = useState<PortfolioAsset | null>(null);
  const [isAddTransactionOpen, setIsAddTransactionOpen] = useState(false);
  const [isExportModalOpen, setIsExportModalOpen] = useState(false);

  const [selectedAssetForAlert, setSelectedAssetForAlert] = useState<PortfolioAsset | null>(null);
  const [isAlertModalOpen, setIsAlertModalOpen] = useState(false);
  const [isAddCoinModalOpen, setIsAddCoinModalOpen] = useState(false);

  // Debug modal state changes
  React.useEffect(() => {
    console.log("Add Coin Modal state değişti:", isAddCoinModalOpen);
  }, [isAddCoinModalOpen]);
  const [isExpanding, setIsExpanding] = useState(false);

  // Get enabled columns for rendering
  const enabledColumns = columns.filter(col => col.enabled);

  // Initialize toast hook

  // Helper to get portfolio icon component
  const getPortfolioIcon = (iconId: string, className: string = "h-4 w-4") => {
    const icons: Record<string, React.ReactElement> = {
      star: <Star className={className} />,
      flame: <Flame className={className} />,
      gem: <Gem className={className} />,
      zap: <Zap className={className} />,
      bookmark: <Bookmark className={className} />,
      heart: <Heart className={className} />,
      award: <Award className={className} />,
      chart: <BarChart3 className={className} />,
      inbox: <Inbox className={className} />,
      layers: <Layers className={className} />,
      share: <Share2 className={className} />,
      shield: <Shield className={className} />,
      dollar: <DollarSign className={className} />,
      briefcase: <Briefcase className={className} />,
      rocket: <Rocket className={className} />
    };
    return icons[iconId] || <Folder className={className} />;
  };

  // Handle column change from layout dialog
  const handleColumnChange = (newColumns: ColumnConfig[]) => {
    setColumns(newColumns);
    setShowLayoutDialog(false);
  };

  // Format currency values
  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 2,
      maximumFractionDigits: 2
    }).format(value);
  };

  // Format percentage values
  const formatPercent = (value: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'percent',
      minimumFractionDigits: 2,
      maximumFractionDigits: 2,
      signDisplay: 'exceptZero'
    }).format(value / 100);
  };

  // Format number values
  const formatNumber = (value: number) => {
    return new Intl.NumberFormat('en-US', {
      minimumFractionDigits: 2,
      maximumFractionDigits: 2
    }).format(value);
  };

  // Handle sort click
  const handleSortClick = (field: SortField) => {
    if (field === sortField) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      setSortField(field);
      setSortDirection('desc'); // Default to descending for new field
    }
  };

  // Handle add transaction
  const handleAddTransaction = async (transactionData: {
    type: 'buy' | 'sell' | 'transfer-in' | 'transfer-out';
    amount: number;
    price: number;
    date: Date;
    notes?: string;
    fees?: number;
  }) => {
    try {
      if (!selectedAsset && !selectedAssetForAddTransaction) {
        console.error('No asset selected for transaction');
        return;
      }

      const asset = selectedAsset || selectedAssetForAddTransaction;
      if (!asset || !portfolio?.id) {
        console.error('Missing asset or portfolio ID');
        return;
      }

      console.log('🚀 Adding transaction:', {
        portfolioId: portfolio.id,
        assetId: asset.id,
        type: transactionData.type,
        amount: transactionData.amount,
        price: transactionData.price
      });

      // Call the API service
      await portfolioService.addTransaction(portfolio.id, {
        assetId: asset.id,
        type: transactionData.type,
        amount: transactionData.amount,
        price: transactionData.price,
        fees: transactionData.fees,
        notes: transactionData.notes,
        executedAt: transactionData.date.toISOString()
      });

      console.log('✅ Transaction added successfully');
      
      // Close the modal
      setTransactionModalOpen(false);
      setIsAddTransactionOpen(false);
      setSelectedAsset(null);
      setSelectedAssetForAddTransaction(null);

      // TODO: You might want to refresh the portfolio data here
      // For now, we'll just show a success message in console
      console.log('Transaction added, consider refreshing portfolio data');

    } catch (error) {
      console.error('❌ Error adding transaction:', error);
      // You might want to show a toast notification here
    }
  };

  // Filter and sort assets
  const filteredAndSortedAssets = [...portfolio.assets]
    .filter(asset =>
      asset.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      asset.symbol.toLowerCase().includes(searchQuery.toLowerCase())
    )
    .sort((a, b) => {
      let aValue: number = 0;
      let bValue: number = 0;

      // Special handling for profit field which is calculated dynamically
      if (sortField === 'profit') {
        aValue = calculateProfitLoss(a);
        bValue = calculateProfitLoss(b);
      } else {
        // Type safety when accessing object properties
        const aField = a[sortField as keyof PortfolioAsset];
        const bField = b[sortField as keyof PortfolioAsset];

        // Ensure we're dealing with numbers for sorting
        aValue = typeof aField === 'number' ? aField : 0;
        bValue = typeof bField === 'number' ? bField : 0;
      }

      const sortMultiplier = sortDirection === 'asc' ? 1 : -1;

      if (aValue < bValue) return -1 * sortMultiplier;
      if (aValue > bValue) return 1 * sortMultiplier;
      return 0;
    });

  // Display only 15 assets initially unless "View More" is clicked
  const displayedAssets = showAllAssets ? filteredAndSortedAssets : filteredAndSortedAssets.slice(0, 15);
  const hasMoreAssets = filteredAndSortedAssets.length > 15;

  // Get AI score status
  const getScoreStatus = (score: number) => {
    if (score >= 90) return { label: 'Excellent', color: 'text-green-400' };
    if (score >= 75) return { label: 'Positive', color: 'text-blue-400' };
    if (score >= 65) return { label: 'Average', color: 'text-amber-400' };
    if (score >= 50) return { label: 'Weak', color: 'text-orange-400' };
    return { label: 'Bad', color: 'text-red-400' };
  };

  // Get status string for ScoreGauge (matches CoinList page)
  const getScoreGaugeStatus = (score: number): CoinStatus => {
    if (score >= 90) return 'Excellent';
    if (score >= 75) return 'Good';
    if (score >= 65) return 'Fair';
    if (score >= 50) return 'Poor';
    return 'Bad';
  };

  // Get color for change percentage
  const getChangeColorClass = (change: number) => {
    return change >= 0 ? 'text-green-400' : 'text-red-400';
  };

  // Calculate profit/loss for an asset (using a placeholder calculation as actual data is not available)
  // In a real implementation, this would use actual purchase price data from transaction history
  const calculateProfitLoss = (asset: PortfolioAsset) => {
    // This is a placeholder calculation, assuming profit is proportional to 24h change
    // In a real implementation, this should be calculated from actual transaction data
    const profitValue = asset.value * (asset.change24h / 200); // Simplified placeholder
    return profitValue;
  };

  // Check if column is enabled
  const isColumnEnabled = (key: string) => {
    const column = columns.find(col => col.key === key);
    return column?.enabled ?? false;
  };

  // Format percentage values for price changes
  const formatPriceChange = (value: number) => {
    return (
      <div className={cn(
        "flex items-center justify-end",
        getChangeColorClass(value)
      )}>
        {value >= 0
          ? <ArrowUpRight className="h-3 w-3 mr-1" />
          : <ArrowDownRight className="h-3 w-3 mr-1" />
        }
        {formatPercent(value)}
      </div>
    );
  };

  // Compact Variant
  if (variant === 'compact') {
    return (
      <>
        <div className={cn("portfolio-assets-container rounded-lg border border-slate-700/40 overflow-hidden transition-all duration-500 ease-in-out", className)}>
          <div className="p-3 border-b border-slate-700/20 bg-slate-800/50 flex flex-col md:flex-row items-start md:items-center justify-between gap-2">
            {!hideHeading && <h3 className="text-primary font-medium">Portfolio Assets</h3>}
            <div className="flex flex-wrap items-center gap-2 w-full md:w-auto">
              <div className="relative max-w-[180px]">
                <Search className="absolute left-2 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                <input
                  placeholder="Search assets..."
                  className="pl-8 h-8 bg-slate-900/50 border border-slate-700/50 rounded-lg text-primary placeholder-slate-400 focus:border-primary/70 focus:outline-none"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                />
                {searchQuery && (
                  <button
                    className="absolute right-2 top-1/2 transform -translate-y-1/2 text-muted-foreground hover:text-primary"
                    onClick={() => setSearchQuery('')}
                  >
                    <XCircle className="h-4 w-4" />
                  </button>
                )}
              </div>

              {/* Compact portfolio control buttons */}
              <div className="flex items-center gap-2 overflow-x-auto hide-scrollbar">
                {/* Portfolio selector dropdown */}
                {portfolios && portfolios.length > 0 && (
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button
                        size="sm"
                        variant="outline"
                        className="h-8 min-w-[140px] justify-start"
                      >
                        <div className={cn(
                          "h-5 w-5 rounded flex items-center justify-center mr-2 flex-shrink-0",
                          portfolio.theme === 'default' || portfolio.theme === 'blue' ? 'bg-blue-600/80' :
                          portfolio.theme === 'green' ? 'bg-green-600/80' :
                          portfolio.theme === 'purple' ? 'bg-purple-600/80' :
                          portfolio.theme === 'amber' ? 'bg-amber-600/80' :
                          portfolio.theme === 'cyan' ? 'bg-cyan-600/80' :
                          portfolio.theme === 'rose' ? 'bg-rose-600/80' : 'bg-blue-600/80'
                        )}>
                          {getPortfolioIcon(portfolio.icon || 'star', 'h-3 w-3 text-white')}
                        </div>
                        <div className="flex-1 text-left">
                          <div className="truncate font-medium">{portfolio.name}</div>
                          {portfolio.description && (
                            <div className="text-xs text-slate-400 truncate">{portfolio.description}</div>
                          )}
                        </div>
                        <ChevronDown className="h-3 w-3 ml-1 opacity-70 flex-shrink-0" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent className="bg-slate-800 border-slate-700/50 text-primary">
                      <DropdownMenuLabel>Select Portfolio</DropdownMenuLabel>
                      <DropdownMenuSeparator className="bg-slate-700/50" />
                      {portfolios.map((p) => (
                        <DropdownMenuItem
                          key={p.id}
                          onClick={() => onSelectPortfolio?.(p.id)}
                          className="!hover:bg-primary !hover:text-white !hover:border-primary transition-colors cursor-pointer flex items-center"
                        >
                          <div className={cn(
                            "h-5 w-5 rounded flex items-center justify-center mr-2 flex-shrink-0",
                            p.theme === 'default' || p.theme === 'blue' ? 'bg-blue-600/80' :
                            p.theme === 'green' ? 'bg-green-600/80' :
                            p.theme === 'purple' ? 'bg-purple-600/80' :
                            p.theme === 'amber' ? 'bg-amber-600/80' :
                            p.theme === 'cyan' ? 'bg-cyan-600/80' :
                            p.theme === 'rose' ? 'bg-rose-600/80' : 'bg-blue-600/80'
                          )}>
                            {getPortfolioIcon(p.icon || 'star', 'h-3 w-3 text-white')}
                          </div>
                          <div className="flex-1">
                            <div className="font-medium">{p.name}</div>
                            {p.description && (
                              <div className="text-xs text-slate-400 truncate mt-0.5">{p.description}</div>
                            )}
                          </div>
                          {p.id === portfolio.id && <Check className="h-4 w-4 ml-2" />}
                        </DropdownMenuItem>
                      ))}

                      {/* Create New Portfolio option */}
                      <DropdownMenuSeparator className="bg-slate-700/50" />
                      <DropdownMenuItem
                        className="!hover:bg-primary !hover:text-white !hover:border-primary transition-colors cursor-pointer text-blue-400"
                        onClick={() => onCreatePortfolio?.()}
                      >
                        <FolderPlus className="h-4 w-4 mr-2" />
                        Create New Portfolio
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                )}

                {/* Edit Button */}
                <Button
                  size="sm"
                  variant="outline"
                  className="h-8"
                  onClick={() => onEditPortfolio?.(portfolio.id)}
                >
                  <PencilLine className="h-4 w-4 mr-1" />
                  Edit
                </Button>

                {/* New Portfolio Button */}
                <Button
                  size="sm"
                  variant="outline"
                  className="h-8"
                  onClick={() => onCreatePortfolio?.()}
                >
                  <FolderPlus className="h-4 w-4 mr-1" />
                  New Portfolio
                </Button>

                {/* Add Coin Button */}
                <Button
                  size="sm"
                  variant="outline"
                  className="h-8 bg-red-500 text-white"
                  onClick={(e) => {
                    e.preventDefault();
                    e.stopPropagation();
                    console.log("🚀 BUTON TIKLANDI! Modal açılıyor...");
                    setIsAddCoinModalOpen(true);
                    console.log("🚀 State değiştirildikten sonra:", true);
                  }}
                >
                  <PlusCircle className="h-4 w-4 mr-1" />
                  Add Coin (TEST)
                </Button>

                {/* Layout Button */}
                <Button
                  size="sm"
                  variant="outline"
                  className="h-8"
                  onClick={() => setShowLayoutDialog(true)}
                >
                  <SlidersHorizontal className="h-4 w-4 mr-1" />
                  Layout
                </Button>

                {/* Export Button */}
                <Button
                  size="sm"
                  variant="outline"
                  className="h-8 hover:!bg-primary hover:!text-white hover:!border-primary"
                  onClick={() => setIsExportModalOpen(true)}
                >
                  <ArrowUpRight className="h-4 w-4 mr-1" />
                  Export
                </Button>

                {/* Share Button */}
                <ShareMechanism
                  itemId={portfolio.id}
                  itemType="portfolio"
                  itemName={portfolio.name}
                  customShareUrl={`/portfolio/analysis?id=${portfolio.id}`}
                >
                  <Button
                    size="sm"
                    variant="outline"
                    className="h-8 hover:!bg-primary hover:!text-white hover:!border-primary"
                  >
                    <Share2 className="h-4 w-4 mr-1" />
                    Share
                  </Button>
                </ShareMechanism>
              </div>
            </div>
          </div>

          {/* Table wrapper with horizontal scroll when needed */}
          <div className="portfolio-table-scroll-container">
            {/* Using the visibility key to force re-render when columns change */}
            <Table
              key={columns.filter(col => col.enabled).map(col => col.key).join(',')}
              className="portfolio-assets-table transition-all duration-500 ease-in-out"
              data-columns={columns.filter(col => col.enabled).length}
              style={{ minWidth: `${200 + 100 + (columns.filter(col => col.enabled && col.key !== 'asset').length * 140)}px` }}
            >
            <TableHeader className="bg-slate-700/40">
              <TableRow className="hover:bg-transparent border-b border-border/30">
                {columns.filter(col => col.enabled).map((column) => {
                  // Determine text alignment based on column type
                  let alignment = "";
                  if (column.key === 'asset') {
                    alignment = "text-left"; // Asset name is left-aligned
                  } else if (['holdings', 'allocation', 'value', 'profitLoss', 'price', 'marketCap',
                             'volume24h', 'fdv', 'priceChange1h', 'priceChange24h', 'priceChange7d',
                             'priceChange30d', 'priceChange60d', 'priceChange180d', 'priceChange1y',
                             'marketCapToFdv', 'circSupply', 'circSupplyPercent', 'totalSupply',
                             'fundraise', 'icoPrice', 'raise'].includes(column.key)) {
                    alignment = "text-right"; // Numeric values are right-aligned
                  } else {
                    alignment = "text-center"; // Scores and other columns are center-aligned
                  }

                  return (
                    <TableHead
                      key={column.key}
                      className={cn(
                        "text-primary hover:bg-muted/70 transition-colors px-3 py-3",
                        alignment
                      )}
                      style={{ width: column.width, minWidth: column.width }}
                    >
                      {column.key === 'totalScore' ? (
                        <span className="flex items-center justify-center">🤖 {column.label}</span>
                      ) : column.label}
                    </TableHead>
                  );
                })}
                <TableHead className="text-primary hover:bg-muted/70 transition-colors text-center px-3 py-3" style={{ width: '100px', minWidth: '100px' }}>Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredAndSortedAssets.length === 0 ? (
                <TableRow>
                  <TableCell colSpan={columns.filter(col => col.enabled).length + 1} className="text-center py-8 text-muted-foreground">
                    No assets found matching your search.
                  </TableCell>
                </TableRow>
              ) : (
                displayedAssets.map((asset, index) => {
                  const isNewRow = index >= 15;
                  const animationDelay = (index - 15) * 100;

                  return (
                    <TableRow
                      key={asset.id}
                      className={cn(
                        "group/row cursor-pointer bg-slate-800/50 border-b border-slate-700/20 !hover:bg-primary !hover:text-white !hover:border-primary transition-colors",
                        isNewRow && showAllAssets && "animate-in fade-in-0 slide-in-from-bottom-5"
                      )}
                      style={{
                        animationDelay: isNewRow && showAllAssets ? `${animationDelay}ms` : undefined,
                        animationDuration: isNewRow && showAllAssets ? '600ms' : undefined,
                        animationFillMode: isNewRow && showAllAssets ? 'both' : undefined
                      }}
                      onClick={() => onAssetClick?.(asset)}
                    >
                    {columns.filter(col => col.enabled).map((column) => {
                      // Render cell based on column key
                      if (column.key === 'asset') {
                        return (
                          <TableCell key={column.key} className="px-3 py-3" style={{ width: column.width, minWidth: column.width }}>
                            <div className="flex items-center space-x-2">
                              <div className="flex-shrink-0">
                                <CoinLogo symbol={asset.symbol.toLowerCase()} size="sm" className="w-5 h-5" />
                              </div>
                              <div className="flex flex-col justify-center">
                                <div className="font-medium text-sm leading-tight">{asset.name}</div>
                                <div className="text-xs text-muted-foreground leading-tight">{asset.symbol}</div>
                              </div>
                            </div>
                          </TableCell>
                        );
                      }

                      if (column.key === 'holdings') {
                        return (
                          <TableCell key={column.key} className="text-right px-3 py-3 whitespace-nowrap">
                            <span className="text-[12px] font-medium">{asset.amount.toFixed(4)}</span>
                          </TableCell>
                        );
                      }

                      if (column.key === 'allocation') {
                        return (
                          <TableCell key={column.key} className="text-right px-3 py-3 whitespace-nowrap">
                            <span className="text-[12px] font-medium">{formatPercent(asset.allocation)}</span>
                          </TableCell>
                        );
                      }

                      if (column.key === 'value') {
                        return (
                          <TableCell key={column.key} className={"text-right px-3 py-3 whitespace-nowrap"}>
                            <span className="text-[12px] font-medium">${formatNumber(asset.value)}</span>
                          </TableCell>
                        );
                      }

                      if (column.key === 'profitLoss') {
                        // Placeholder calculation - in real implementation would use actual purchase data
                        const profitLoss = calculateProfitLoss(asset);
                        const profitLossPercent = (profitLoss / asset.value) * 100;
                        return (
                          <TableCell key={column.key} className={"text-right px-3 py-3 whitespace-nowrap"}>
                            <div className="flex flex-col items-end">
                              <span className={cn(
                                "text-[12px] font-medium",
                                profitLoss >= 0 ? "text-green-400" : "text-red-400"
                              )}>
                                ${formatNumber(Math.abs(profitLoss))}
                              </span>
                              <span className={cn(
                                "text-[10px]",
                                profitLoss >= 0 ? "text-green-400/70" : "text-red-400/70"
                              )}>
                                ({formatPercent(profitLossPercent)})
                              </span>
                            </div>
                          </TableCell>
                        );
                      }

                      if (column.key === 'price') {
                        return (
                          <TableCell key={column.key} className={"text-right px-3 py-3 whitespace-nowrap"}>
                            <span className="text-[12px] font-medium">${formatNumber(asset.price)}</span>
                          </TableCell>
                        );
                      }

                      if (column.key === 'marketCap') {
                        return (
                          <TableCell key={column.key} className={"text-right px-3 py-3 whitespace-nowrap"}>
                            <span className="text-[12px] font-medium">${formatNumber(asset.marketCap || 0)}</span>
                          </TableCell>
                        );
                      }

                      if (column.key === 'priceChange24h' || column.key === 'priceChange1h' || column.key === 'priceChange7d' ||
                          column.key === 'priceChange30d' || column.key === 'priceChange60d' || column.key === 'priceChange180d' ||
                          column.key === 'priceChange1y') {
                        const changeValue = asset[column.key as keyof PortfolioAsset] as number || asset.change24h;
                        return (
                          <TableCell key={column.key} className={"text-right px-3 py-3 whitespace-nowrap"}>
                            <span className={cn(
                              "flex items-center justify-end text-[12px] font-medium",
                              getChangeColorClass(changeValue)
                            )}>
                              {changeValue >= 0
                                ? <ArrowUpRight className="h-3 w-3 mr-1" />
                                : <ArrowDownRight className="h-3 w-3 mr-1" />
                              }
                              {formatPercent(changeValue)}
                            </span>
                          </TableCell>
                        );
                      }

                      if (column.key === 'totalScore') {
                        return (
                          <TableCell key={column.key} className={"px-3 py-3"}>
                            <div className="flex items-center justify-center">
                              <AiScoreBadge score={asset.aiScore || 0} size="sm" />
                            </div>
                          </TableCell>
                        );
                      }

                      if (column.key === 'volume24h') {
                        return (
                          <TableCell key={column.key} className={"text-right px-3 py-3 whitespace-nowrap"}>
                            <span className="text-[12px] font-medium">${formatNumber(asset.volume24h || 0)}</span>
                          </TableCell>
                        );
                      }

                      if (column.key === 'fdv') {
                        return (
                          <TableCell key={column.key} className={"text-right px-3 py-3 whitespace-nowrap"}>
                            <span className="text-[12px] font-medium">${formatNumber(asset.fdv || 0)}</span>
                          </TableCell>
                        );
                      }

                      if (column.key === 'marketCapToFdv') {
                        const ratio = (asset.marketCap && asset.fdv) ? asset.marketCap / asset.fdv : 0;
                        return (
                          <TableCell key={column.key} className="text-right px-3 py-3 whitespace-nowrap">
                            <span className="text-[12px] font-medium">{(ratio * 100).toFixed(1)}%</span>
                          </TableCell>
                        );
                      }

                      if (column.key === 'age') {
                        const ageInDays = asset.coinAge || 0;
                        return (
                          <TableCell key={column.key} className={"text-center px-3 py-3 whitespace-nowrap"}>
                            <span className="text-[11px] font-normal bg-blue-500/10 text-blue-400 border border-blue-500/20 px-2 py-1 rounded">
                              {ageInDays}d
                            </span>
                          </TableCell>
                        );
                      }

                      if (column.key === 'category') {
                        return (
                          <TableCell key={column.key} className={"text-center px-3 py-3 whitespace-nowrap"}>
                            <span className="text-[12px] font-medium">
                              {asset.category || '-'}
                            </span>
                          </TableCell>
                        );
                      }

                      if (column.key === 'listingDate') {
                        return (
                          <TableCell key={column.key} className={"text-center px-3 py-3 whitespace-nowrap"}>
                            <span className="text-[12px] font-medium">
                              {asset.listingDate ? new Date(asset.listingDate).toLocaleDateString() : '-'}
                            </span>
                          </TableCell>
                        );
                      }

                      // Tokenomics columns
                      if (column.key === 'circSupply') {
                        return (
                          <TableCell key={column.key} className={"text-right px-3 py-3 whitespace-nowrap"}>
                            <span className="text-[12px] font-medium">
                              {asset.circSupply ? formatNumber(asset.circSupply) : '-'}
                            </span>
                          </TableCell>
                        );
                      }

                      if (column.key === 'circSupplyPercent') {
                        return (
                          <TableCell key={column.key} className={"text-right px-3 py-3 whitespace-nowrap"}>
                            <span className="text-[12px] font-medium">
                              {asset.circSupplyPercent ? `${asset.circSupplyPercent.toFixed(1)}%` : '-'}
                            </span>
                          </TableCell>
                        );
                      }

                      if (column.key === 'totalSupply') {
                        return (
                          <TableCell key={column.key} className="text-right px-3 py-3 whitespace-nowrap">
                            <span className="text-[12px] font-medium">
                              {asset.totalSupply ? formatNumber(asset.totalSupply) : '-'}
                            </span>
                          </TableCell>
                        );
                      }

                      // Fundraising columns
                      if (column.key === 'fundraise') {
                        return (
                          <TableCell key={column.key} className="text-center px-3 py-3 whitespace-nowrap">
                            <span className="text-[12px] font-medium">
                              {asset.fundraise || '-'}
                            </span>
                          </TableCell>
                        );
                      }

                      if (column.key === 'icoPrice') {
                        return (
                          <TableCell key={column.key} className="text-right px-3 py-3 whitespace-nowrap">
                            <span className="text-[12px] font-medium">
                              {asset.icoPrice ? `$${formatNumber(asset.icoPrice)}` : '-'}
                            </span>
                          </TableCell>
                        );
                      }

                      if (column.key === 'raise') {
                        return (
                          <TableCell key={column.key} className="text-right px-3 py-3 whitespace-nowrap">
                            <span className="text-[12px] font-medium">
                              {asset.raise ? `$${formatNumber(asset.raise)}` : '-'}
                            </span>
                          </TableCell>
                        );
                      }

                      if (column.key === 'icoDate') {
                        return (
                          <TableCell key={column.key} className="text-center px-3 py-3 whitespace-nowrap">
                            <span className="text-[12px] font-medium">
                              {asset.icoDate ? new Date(asset.icoDate).toLocaleDateString() : '-'}
                            </span>
                          </TableCell>
                        );
                      }

                      // Score columns
                      if (column.key === 'security' || column.key === 'social' || column.key === 'market' ||
                          column.key === 'tokenomics' || column.key === 'insights') {
                        const scoreValue = asset[column.key as keyof PortfolioAsset] as number || 0;
                        return (
                          <TableCell key={column.key} className={"px-3 py-3"}>
                            <div className="flex items-center justify-center">
                              <ScoreGauge score={scoreValue} size="sm" showLabel={false} />
                            </div>
                          </TableCell>
                        );
                      }

                      // Seven day change
                      if (column.key === 'sevenDayChange') {
                        const changeValue = asset.sevenDayChange || 0;
                        return (
                          <TableCell key={column.key} className={"px-3 py-3"}>
                            <div className="flex items-center justify-center">
                              <span className={cn(
                                "text-[12px] font-medium",
                                changeValue >= 0 ? "text-green-400" : "text-red-400"
                              )}>
                                {changeValue >= 0 ? "+" : ""}{changeValue}
                              </span>
                            </div>
                          </TableCell>
                        );
                      }

                      // Default rendering for other columns
                      return (
                        <TableCell key={column.key} className={"text-center px-3 py-3 whitespace-nowrap"}>
                          <span className="text-[12px] font-medium text-muted-foreground">-</span>
                        </TableCell>
                      );
                    })}

                    {/* Actions column - simplified to single View button */}
                    <TableCell className="text-center px-3 py-3">
                      <Button
                        size="sm"
                        variant="outline"
                        className="h-8 px-3"
                        onClick={(e) => {
                          e.stopPropagation();
                          onAssetClick?.(asset);
                        }}
                      >
                        View
                      </Button>
                    </TableCell>
                  </TableRow>
                  );
                })
              )}
            </TableBody>
          </Table>
          </div>
        </div>

        {/* View More Button for compact variant */}
        {hasMoreAssets && !showAllAssets && (
          <div className="flex justify-center py-4">
            <Button
              variant="outline"
              size="sm"
              onClick={() => {
                setIsExpanding(true);
                setTimeout(() => {
                  setShowAllAssets(true);
                  setIsExpanding(false);
                }, 50);
              }}
            >
              View All Assets ({filteredAndSortedAssets.length - 15} more)
            </Button>
          </div>
        )}

        {/* View Less Button for compact variant */}
        {showAllAssets && filteredAndSortedAssets.length > 15 && (
          <div className="flex justify-center py-4">
            <Button
              variant="outline"
              size="sm"
              onClick={() => {
                setIsExpanding(true);
                setTimeout(() => {
                  setShowAllAssets(false);
                  setIsExpanding(false);
                }, 50);
              }}
            >
              View Less
            </Button>
          </div>
        )}

        {/* Add Transaction Modal for compact variant */}
        <AddTransactionModal
          isOpen={transactionModalOpen}
          onClose={() => setTransactionModalOpen(false)}
          asset={selectedAsset}
          onAddTransaction={handleAddTransaction}
        />

        {/* Transaction History Modal for compact variant */}
        <TransactionHistoryModal
          isOpen={transactionHistoryModalOpen}
          onClose={() => setTransactionHistoryModalOpen(false)}
          asset={selectedAsset}
        />

        {/* Portfolio Customization Modal for compact variant */}


        {/* Create Portfolio Modal for compact variant */}
        {/* Column Layout Dialog */}
        {showLayoutDialog && (
          <TableLayoutModal
            columns={columns}
            defaultColumns={DEFAULT_COLUMNS}
            onColumnChange={handleColumnChange}
            onClose={() => setShowLayoutDialog(false)}
            className="max-w-2xl"
            categories={portfolioCategories}
            requiredColumn="asset"
          />
        )}
      </>
    );
  }

  // Use default variant rendering for detailed variant as well
  if (variant === 'detailed') {
    // Redirect to default variant to avoid duplicate table implementations
    variant = 'default';
  }

        {/* Dynamic Table with Configurable Columns */}
        <div className={cn(
          "transition-all duration-700 ease-in-out relative",
          showAllAssets && filteredAndSortedAssets.length > 15 ? "max-h-[2000px]" : "max-h-[800px]",
          "overflow-x-auto",
          showAllAssets && "before:absolute before:inset-0 before:bg-gradient-to-b before:from-blue-500/5 before:to-transparent before:pointer-events-none before:animate-pulse"
        )}>
          <Table key={columns.filter(col => col.enabled).map(col => col.key).join(',')} className="portfolio-assets-table">
            <TableHeader className="bg-slate-700/40">
            <TableRow className="hover:bg-transparent border-b border-border/30">
              {/* Dynamic column headers based on enabled columns */}
              {columns.filter(col => col.enabled).map((column) => (
                <TableHead
                  key={column.key}
                  className={cn(
                    "text-primary hover:bg-muted/70 transition-colors px-3 py-3",
                    // Match header alignment with cell alignment
                    column.key === 'asset' ? "text-left" :
                    ['holdings', 'value', 'allocation', 'price', 'priceChange1h', 'priceChange24h',
                     'priceChange7d', 'priceChange30d', 'priceChange60d', 'priceChange180d',
                     'priceChange1y', 'marketCap', 'volume24h', 'fdv', 'profitLoss', 'circSupply',
                     'totalSupply', 'icoPrice', 'marketCapToFdv', 'circSupplyPercent', 'fundraise',
                     'raise'].includes(column.key) ? "text-right" : "text-center"
                  )}
                >
                  {column.key === 'totalScore' ? (
                    <span className="flex items-center justify-center">🤖 {column.label}</span>
                  ) : column.label}
                </TableHead>
              ))}

              {/* Actions Column (Fixed) - Center Aligned */}
              <TableHead className="text-primary hover:bg-muted/70 transition-colors text-center px-3 py-3">Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {filteredAndSortedAssets.length === 0 ? (
              <TableRow>
                <TableCell
                  colSpan={columns.filter(col => col.enabled).length + 1}
                  className="text-center py-8 text-muted-foreground"
                >
                  No assets found matching your search.
                </TableCell>
              </TableRow>
            ) : (
              displayedAssets.map((asset, index) => (
                <TableRow
                  key={`${asset.symbol}-${asset.id}`}
                  className={cn(
                    "group/row cursor-pointer hover:bg-muted/30 transition-all duration-300",
                    index >= 15 && showAllAssets && "portfolio-row-enter"
                  )}
                  style={{
                    animationDelay: index >= 15 && showAllAssets ? `${(index - 15) * 100}ms` : undefined
                  }}
                  onClick={() => onAssetClick?.(asset)}
                >
                  {/* Render cells based on enabled columns */}
                  {columns.filter(col => col.enabled).map((column) => {
                    // Asset column
                    if (column.key === 'asset') {
                      return (
                        <TableCell key={column.key} className="px-3 py-3">
                          <div className="flex items-center space-x-2">
                            <div className="flex-shrink-0">
                              <CoinLogo symbol={asset.symbol.toLowerCase()} size="sm" className="w-5 h-5" />
                            </div>
                            <div className="flex flex-col justify-center">
                              <div className="font-medium text-sm leading-tight">{asset.name}</div>
                              <div className="text-xs text-muted-foreground leading-tight">{asset.symbol}</div>
                            </div>
                          </div>
                        </TableCell>
                      );
                    }

                    // Holdings column
                    if (column.key === 'holdings') {
                      return (
                        <TableCell key={column.key} className="text-right px-3 py-3 whitespace-nowrap">
                          <span className="text-[12px] font-medium">{asset.amount.toFixed(6)} {asset.symbol}</span>
                        </TableCell>
                      );
                    }

                    // Value column
                    if (column.key === 'value') {
                      return (
                        <TableCell key={column.key} className="text-right px-3 py-3 whitespace-nowrap">
                          <span className="text-[12px] font-medium">{formatCurrency(asset.value)}</span>
                        </TableCell>
                      );
                    }

                    // Allocation column
                    if (column.key === 'allocation') {
                      return (
                        <TableCell key={column.key} className="text-right px-3 py-3 whitespace-nowrap">
                          <span className="text-[12px] font-medium">{asset.allocation.toFixed(2)}%</span>
                        </TableCell>
                      );
                    }

                    // Price column
                    if (column.key === 'price') {
                      return (
                        <TableCell key={column.key} className="text-right px-3 py-3 whitespace-nowrap">
                          <span className="text-[12px] font-medium">{formatCurrency(asset.price || 0)}</span>
                        </TableCell>
                      );
                    }

                    // Market Cap column
                    if (column.key === 'marketCap') {
                      return (
                        <TableCell key={column.key} className="text-right px-3 py-3 whitespace-nowrap">
                          <span className="text-[12px] font-medium">{formatCurrency(asset.marketCap || 0)}</span>
                        </TableCell>
                      );
                    }

                    // Total Score column
                    if (column.key === 'totalScore') {
                      return (
                        <TableCell key={column.key} className="px-3 py-3 whitespace-nowrap">
                          <div className="flex items-center justify-center">
                            <ScoreGauge
                              score={asset.aiScore || 0}
                              size="sm"
                              showLabel={false}
                            />
                          </div>
                        </TableCell>
                      );
                    }

                    // P/L column
                    if (column.key === 'profitLoss') {
                      const pnl = calculateProfitLoss(asset);
                      return (
                        <TableCell key={column.key} className="text-right px-3 py-3 whitespace-nowrap">
                          <span className={cn(
                            "text-[12px] font-medium",
                            pnl > 0 ? "text-green-500" : pnl < 0 ? "text-red-500" : "text-muted-foreground"
                          )}>
                            {pnl > 0 ? "+" : ""}{pnl.toFixed(2)}%
                          </span>
                        </TableCell>
                      );
                    }

                    return null;
                  })}

                  {/* Actions column (always visible) */}
                  <TableCell className="text-center px-3 py-3 whitespace-nowrap">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={(e) => {
                        e.stopPropagation();
                        setSelectedAssetForDetail(asset);
                        setIsAssetDetailOpen(true);
                      }}
                      className="h-8 px-3"
                    >
                      View
                    </Button>
                  </TableCell>
                </TableRow>
              ))
            )}
          </TableBody>
          </Table>
        </div>








  // Default variant
  return (
    <>
      <div className={cn("portfolio-assets-container rounded-lg border border-slate-700/40 overflow-hidden transition-all duration-500 ease-in-out", className)}>
        <div className="p-3 border-b border-slate-700/20 bg-slate-800/50 flex items-center justify-between">
          {!hideHeading && <h3 className="text-primary font-medium">Portfolio Assets</h3>}
          <div className="flex items-center gap-2">
            <div className="flex items-center gap-2 bg-slate-800/50 rounded-lg px-2 py-1">
              <Search className="h-4 w-4 text-muted-foreground" />
              <input
                type="text"
                placeholder="Search assets..."
                className="bg-slate-900/50 border border-slate-700/50 rounded-lg text-sm text-white placeholder-slate-400 focus:border-primary/70 focus:outline-none px-2 py-1"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
              />
              {searchQuery && (
                <button
                  className="text-muted-foreground hover:text-primary"
                  onClick={() => setSearchQuery('')}
                >
                  <XCircle className="h-3 w-3" />
                </button>
              )}
            </div>

            <div className="w-px h-6 bg-slate-700/40"></div>

            {/* Portfolio Management Section - Consistent styling */}
            <div className="flex items-center gap-2 overflow-x-auto hide-scrollbar">
              {/* Portfolio selector dropdown */}
              {portfolios && portfolios.length > 0 && (
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button
                      size="sm"
                      variant="outline"
                      className="h-8 bg-transparent border-slate-700/40 text-slate-200 hover:bg-slate-700/30 hover:text-white transition-colors"
                    >
                      <Folder className="h-4 w-4 mr-1" />
                      Portfolios
                      <ChevronDown className="h-3 w-3 ml-1 opacity-70" />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent className="bg-slate-800 border-slate-700/50 text-primary">
                    <DropdownMenuLabel>Select Portfolio</DropdownMenuLabel>
                    <DropdownMenuSeparator className="bg-slate-700/50" />
                    {portfolios.map((p) => (
                      <DropdownMenuItem
                        key={p.id}
                        onClick={() => onSelectPortfolio?.(p.id)}
                        className="!hover:bg-primary !hover:text-white !hover:border-primary transition-colors cursor-pointer"
                      >
                        {p.name}
                        {p.id === portfolio.id && <Check className="h-4 w-4 ml-2" />}
                      </DropdownMenuItem>
                    ))}

                    {/* Create New Portfolio option */}
                    <DropdownMenuSeparator className="bg-slate-700/50" />
                    <DropdownMenuItem
                      className="!hover:bg-primary !hover:text-white !hover:border-primary transition-colors cursor-pointer text-blue-400"
                      onClick={() => onCreatePortfolio?.()}
                    >
                      <FolderPlus className="h-4 w-4 mr-2" />
                      Create New Portfolio
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              )}

              {/* Edit Button */}
              <Button
                size="sm"
                variant="outline"
                className="h-8 bg-transparent border-slate-700/40 text-slate-200 hover:bg-slate-700/30 hover:text-white transition-colors"
                onClick={() => onEditPortfolio?.(portfolio.id)}
              >
                <Pencil className="h-4 w-4 mr-1" />
                Edit
              </Button>

              <div className="w-px h-6 bg-slate-700/40"></div>

              {/* New Portfolio Button - Primary action with blue styling */}
              <Button
                size="sm"
                variant="outline"
                className="h-8 bg-blue-600 text-white hover:bg-blue-700 border-blue-600 transition-colors"
                onClick={() => onCreatePortfolio?.()}
              >
                <Plus className="h-4 w-4 mr-1" />
                New Portfolio
              </Button>

              {/* Add Coin Button */}
              <Button
                size="sm"
                variant="outline"
                className="h-8 bg-slate-800/60 border-slate-700/40 text-slate-200 hover:bg-slate-800/80 hover:text-white transition-colors"
                onClick={() => {
                  console.log("🚀 Add Coin button clicked - opening modal");
                  setIsAddCoinModalOpen(true);
                }}
              >
                <Plus className="h-4 w-4 mr-1" />
                Add Coin
              </Button>

              <div className="w-px h-6 bg-slate-700/40"></div>

              {/* Layout Button */}
              <Button
                size="sm"
                onClick={() => setShowLayoutDialog(true)}
                variant="outline"
                className="h-8 bg-slate-800/60 border-slate-700/40 text-slate-200 hover:bg-slate-800/80 hover:text-white transition-colors"
              >
                <BarChart3 className="h-4 w-4 mr-1" />
                Layout
              </Button>
            </div>
          </div>
        </div>

        {/* Using the visibility key to force re-render when columns change */}
        <div className={cn(
          "transition-all duration-700 ease-in-out relative",
          showAllAssets && filteredAndSortedAssets.length > 15 ? "max-h-[2000px]" : "max-h-[800px]",
          "overflow-x-auto",
          showAllAssets && "before:absolute before:inset-0 before:bg-gradient-to-b before:from-blue-500/5 before:to-transparent before:pointer-events-none before:animate-pulse"
        )}>
          <Table key={columns.filter(col => col.enabled).map(col => col.key).join(',')} className="portfolio-assets-table">
            <TableHeader className="bg-slate-700/40">
            <TableRow className="hover:bg-transparent border-b border-border/30">
              {/* Dynamic column headers based on enabled columns */}
              {columns.filter(col => col.enabled).map((column) => (
                <TableHead
                  key={column.key}
                  className={cn(
                    "text-primary hover:bg-muted/70 transition-colors px-3 py-3",
                    // Match header alignment with cell alignment
                    column.key === 'asset' ? "text-left" :
                    ['holdings', 'value', 'allocation', 'price', 'priceChange1h', 'priceChange24h',
                     'priceChange7d', 'priceChange30d', 'priceChange60d', 'priceChange180d',
                     'priceChange1y', 'marketCap', 'volume24h', 'fdv', 'profitLoss', 'circSupply',
                     'totalSupply', 'icoPrice', 'marketCapToFdv', 'circSupplyPercent', 'fundraise',
                     'raise'].includes(column.key) ? "text-right" : "text-center"
                  )}
                >
                  {column.key === 'totalScore' ? (
                    <span className="flex items-center justify-center">🤖 {column.label}</span>
                  ) : column.label}
                </TableHead>
              ))}

              {/* Actions Column (Fixed) - Center Aligned */}
              <TableHead className="text-primary hover:bg-muted/70 transition-colors text-center px-3 py-3">Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {filteredAndSortedAssets.length === 0 ? (
              <TableRow>
                <TableCell
                  colSpan={columns.filter(col => col.enabled).length + 1}
                  className="text-center py-8 text-muted-foreground"
                >
                  No assets found matching your search.
                </TableCell>
              </TableRow>
            ) : (
              displayedAssets.map((asset, index) => (
                <TableRow
                  key={`${asset.symbol}-${asset.id}`}
                  className={cn(
                    "group/row cursor-pointer hover:bg-muted/30 transition-all duration-300",
                    index >= 15 && showAllAssets && "portfolio-row-enter"
                  )}
                  style={{
                    animationDelay: index >= 15 && showAllAssets ? `${(index - 15) * 100}ms` : undefined
                  }}
                  onClick={() => onAssetClick?.(asset)}
                >
                  {/* Render cells based on enabled columns */}
                  {columns.filter(col => col.enabled).map((column) => {
                    // Asset column
                    if (column.key === 'asset') {
                      return (
                        <TableCell key={column.key} className="px-3 py-3">
                          <div className="flex items-center space-x-2">
                            <div className="flex-shrink-0">
                              <CoinLogo symbol={asset.symbol.toLowerCase()} size="sm" className="w-5 h-5" />
                            </div>
                            <div className="flex flex-col justify-center">
                              <div className="font-medium text-sm leading-tight">{asset.name}</div>
                              <div className="text-xs text-muted-foreground leading-tight">{asset.symbol}</div>
                            </div>
                          </div>
                        </TableCell>
                      );
                    }

                    // Holdings column
                    if (column.key === 'holdings') {
                      return (
                        <TableCell key={column.key} className="text-right px-3 py-3 whitespace-nowrap">
                          <span className="text-[12px] font-medium">{asset.amount.toFixed(6)} {asset.symbol}</span>
                        </TableCell>
                      );
                    }

                    // Value column
                    if (column.key === 'value') {
                      return (
                        <TableCell key={column.key} className="text-right px-3 py-3 whitespace-nowrap">
                          <span className="text-[12px] font-medium">{formatCurrency(asset.value)}</span>
                        </TableCell>
                      );
                    }

                    // Allocation column
                    if (column.key === 'allocation') {
                      return (
                        <TableCell key={column.key} className="text-right px-3 py-3 whitespace-nowrap">
                          <span className="text-[12px] font-medium">{asset.allocation.toFixed(2)}%</span>
                        </TableCell>
                      );
                    }

                    // Price column
                    if (column.key === 'price') {
                      return (
                        <TableCell key={column.key} className="text-right px-3 py-3 whitespace-nowrap">
                          <span className="text-[12px] font-medium">{formatCurrency(asset.price || 0)}</span>
                        </TableCell>
                      );
                    }

                    // Price change columns
                    if (column.key === 'priceChange1h') {
                      return (
                        <TableCell key={column.key} className="text-right px-3 py-3 whitespace-nowrap">
                          <span className={cn(
                            "text-[12px] font-medium",
                            (asset.change1y || 0) > 0 ? "text-green-500" : (asset.change1y || 0) < 0 ? "text-red-500" : "text-muted-foreground"
                          )}>
                            {(asset.change1y || 0) > 0 ? "+" : ""}{(asset.change1y || 0).toFixed(2)}%
                          </span>
                        </TableCell>
                      );
                    }

                    if (column.key === 'priceChange24h') {
                      return (
                        <TableCell key={column.key} className="text-right px-3 py-3 whitespace-nowrap">
                          <span className={cn(
                            "text-[12px] font-medium",
                            asset.change24h > 0 ? "text-green-500" : asset.change24h < 0 ? "text-red-500" : "text-muted-foreground"
                          )}>
                            {asset.change24h > 0 ? "+" : ""}{asset.change24h.toFixed(2)}%
                          </span>
                        </TableCell>
                      );
                    }

                    if (column.key === 'priceChange7d') {
                      return (
                        <TableCell key={column.key} className="text-right px-3 py-3 whitespace-nowrap">
                          <span className={cn(
                            "text-[12px] font-medium",
                            (asset.change7d || 0) > 0 ? "text-green-500" : (asset.change7d || 0) < 0 ? "text-red-500" : "text-muted-foreground"
                          )}>
                            {(asset.change7d || 0) > 0 ? "+" : ""}{(asset.change7d || 0).toFixed(2)}%
                          </span>
                        </TableCell>
                      );
                    }

                    // 30d change column
                    if (column.key === 'priceChange30d') {
                      return (
                        <TableCell key={column.key} className="text-right px-3 py-3 whitespace-nowrap">
                          <span className={cn(
                            "text-[12px] font-medium",
                            (asset.change30d || 0) > 0 ? "text-green-500" : (asset.change30d || 0) < 0 ? "text-red-500" : "text-muted-foreground"
                          )}>
                            {(asset.change30d || 0) > 0 ? "+" : ""}{(asset.change30d || 0).toFixed(2)}%
                          </span>
                        </TableCell>
                      );
                    }

                    // 60d change column
                    if (column.key === 'priceChange60d') {
                      return (
                        <TableCell key={column.key} className="text-right px-3 py-3 whitespace-nowrap">
                          <span className={cn(
                            "text-[12px] font-medium",
                            (asset.change60d || 0) > 0 ? "text-green-500" : (asset.change60d || 0) < 0 ? "text-red-500" : "text-muted-foreground"
                          )}>
                            {(asset.change60d || 0) > 0 ? "+" : ""}{(asset.change60d || 0).toFixed(2)}%
                          </span>
                        </TableCell>
                      );
                    }

                    // These columns don't have data in PortfolioAsset type, return empty cells
                    if (column.key === 'priceChange180d' || column.key === 'priceChange1y' ||
                        column.key === 'age' || column.key === 'category' || column.key === 'listingDate') {
                      return (
                        <TableCell key={column.key} className="text-center px-3 py-3 whitespace-nowrap">
                          <span className="text-[12px] font-medium text-muted-foreground">-</span>
                        </TableCell>
                      );
                    }

                    // Market Cap column
                    if (column.key === 'marketCap') {
                      return (
                        <TableCell key={column.key} className="text-right px-3 py-3 whitespace-nowrap">
                          <span className="text-[12px] font-medium">{formatCurrency(asset.marketCap || 0)}</span>
                        </TableCell>
                      );
                    }

                    // 24h Volume column
                    if (column.key === 'volume24h') {
                      return (
                        <TableCell key={column.key} className="text-right px-3 py-3 whitespace-nowrap">
                          <span className="text-[12px] font-medium">{formatCurrency(asset.volume24h || 0)}</span>
                        </TableCell>
                      );
                    }

                    // FDV column
                    if (column.key === 'fdv') {
                      return (
                        <TableCell key={column.key} className="text-right px-3 py-3 whitespace-nowrap">
                          <span className="text-[12px] font-medium">{formatCurrency(asset.fdv || 0)}</span>
                        </TableCell>
                      );
                    }

                    // MC/FDV Ratio column
                    if (column.key === 'marketCapToFdv') {
                      const ratio = asset.marketCap && asset.fdv ? (asset.marketCap / asset.fdv) : 0;
                      return (
                        <TableCell key={column.key} className="text-right px-3 py-3 whitespace-nowrap">
                          <span className="text-[12px] font-medium">{(ratio * 100).toFixed(1)}%</span>
                        </TableCell>
                      );
                    }

                    // These columns don't have data in PortfolioAsset type, return empty cells
                    if (column.key === 'circSupply' || column.key === 'circSupplyPercent' ||
                        column.key === 'totalSupply' || column.key === 'fundraise' ||
                        column.key === 'icoPrice' || column.key === 'raise' ||
                        column.key === 'icoDate' || column.key === 'security' ||
                        column.key === 'social' || column.key === 'market' ||
                        column.key === 'tokenomics' || column.key === 'insights' ||
                        column.key === 'sevenDayChange') {
                      return (
                        <TableCell key={column.key} className="text-center px-3 py-3 whitespace-nowrap">
                          <span className="text-[12px] font-medium text-muted-foreground">-</span>
                        </TableCell>
                      );
                    }

                    // Total AI Score column
                    if (column.key === 'totalScore') {
                      return (
                        <TableCell key={column.key} className="px-3 py-3 whitespace-nowrap">
                          <div className="flex items-center justify-center">
                            <ScoreGauge
                              score={asset.aiScore}
                              status={getScoreGaugeStatus(asset.aiScore)}
                              size="sm"
                              showLabel={false}
                            />
                          </div>
                        </TableCell>
                      );
                    }

                    // P/L column
                    if (column.key === 'profitLoss') {
                      const pnl = calculateProfitLoss(asset);
                      return (
                        <TableCell key={column.key} className="text-right px-3 py-3 whitespace-nowrap">
                          <span className={cn(
                            "text-[12px] font-medium",
                            pnl > 0 ? "text-green-500" : pnl < 0 ? "text-red-500" : "text-muted-foreground"
                          )}>
                            {pnl > 0 ? "+" : ""}{pnl.toFixed(2)}%
                          </span>
                        </TableCell>
                      );
                    }

                    return null;
                  })}

                  {/* Actions column (always visible) */}
                  <TableCell className="text-center px-3 py-3 whitespace-nowrap">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={(e) => {
                        e.stopPropagation();
                        setSelectedAssetForDetail(asset);
                        setIsAssetDetailOpen(true);
                      }}
                      className="h-8 px-3"
                    >
                      View
                    </Button>
                  </TableCell>
                </TableRow>
              ))
            )}
          </TableBody>
        </Table>
        </div>

        {/* View More Button */}
      {hasMoreAssets && !showAllAssets && (
        <div className="flex justify-center py-4 animate-in fade-in-0 slide-in-from-bottom-2 duration-500">
          <Button
            variant="outline"
            size="sm"
            onClick={() => {
              setShowAllAssets(true);
            }}
          >
            <span className="flex items-center gap-2">
              View All Assets ({filteredAndSortedAssets.length - 15} more)
              <ChevronDown className="h-4 w-4" />
            </span>
          </Button>
        </div>
      )}

      {/* View Less Button */}
      {showAllAssets && filteredAndSortedAssets.length > 15 && (
        <div className="flex justify-center py-4 animate-in fade-in-0 slide-in-from-top-2 duration-500">
          <Button
            variant="outline"
            size="sm"
            onClick={() => {
              setShowAllAssets(false);
            }}
          >
            <span className="flex items-center gap-2">
              <ChevronUp className="h-4 w-4" />
              View Less
            </span>
          </Button>
        </div>
      )}

      {/* Layout Modal */}
      {showLayoutDialog && (
        <TableLayoutModal
          columns={columns}
          defaultColumns={DEFAULT_COLUMNS}
          onColumnChange={handleColumnChange}
          onClose={() => setShowLayoutDialog(false)}
          className="max-w-2xl"
          categories={portfolioCategories}
          requiredColumn="asset"
        />
      )}
      </div>

      {/* Transaction Modals */}
      <AddTransactionModal
        isOpen={transactionModalOpen}
        onClose={() => setTransactionModalOpen(false)}
        asset={selectedAsset}
        onAddTransaction={handleAddTransaction}
      />

      <TransactionHistoryModal
        isOpen={transactionHistoryModalOpen}
        onClose={() => setTransactionHistoryModalOpen(false)}
        asset={selectedAsset}
      />

      {/* Add Coin Modal */}
      <AddCoinModal
        isOpen={isAddCoinModalOpen}
        onClose={() => setIsAddCoinModalOpen(false)}
        portfolio={portfolio}
        onAddAsset={(newAsset) => {
          if (onAddCoin) {
            onAddCoin();
          }
          // Close modal after adding
          setIsAddCoinModalOpen(false);
        }}
      />

      {/* Asset Detail Modal */}
      {selectedAssetForDetail && (
        <AssetDetailModal
          isOpen={isAssetDetailOpen}
          onClose={() => {
            setIsAssetDetailOpen(false);
            setSelectedAssetForDetail(null);
          }}
          asset={selectedAssetForDetail}
          onAddTransaction={() => {
            setSelectedAsset(selectedAssetForDetail);
            setTransactionModalOpen(true);
          }}
          onViewHistory={() => {
            setSelectedAsset(selectedAssetForDetail);
            setTransactionHistoryModalOpen(true);
          }}
          onCreateAlert={() => {
            setSelectedAssetForAlert(selectedAssetForDetail);
            setIsAlertModalOpen(true);
          }}
          onRemoveAsset={() => {
            if (onRemoveCoin) {
              onRemoveCoin(selectedAssetForDetail);
            }
            setIsAssetDetailOpen(false);
            setSelectedAssetForDetail(null);
          }}
          onViewFullDetails={() => {
            // Navigate to coin detail page
            window.location.href = `/coin/${selectedAssetForDetail.coinId}`;
          }}
        />
      )}

      {/* Export Portfolio Modal */}
      {isExportModalOpen && (
        <ExportPortfolioModal
          isOpen={isExportModalOpen}
          onClose={() => setIsExportModalOpen(false)}
          portfolio={portfolio}
        />
      )}



      {/* Alert Modal */}
      {selectedAssetForAlert && (
        <CreateAlertDialogMinimal
          open={isAlertModalOpen}
          onOpenChange={(open) => {
            if (!open) {
              setIsAlertModalOpen(false);
              setSelectedAssetForAlert(null);
            }
          }}
          selectedCoin={selectedAssetForAlert.name}
        />
      )}

      {/* Add Transaction Modal - for row actions */}
      {selectedAssetForAddTransaction && (
        <AddTransactionModal
          isOpen={isAddTransactionOpen}
          onClose={() => {
            setIsAddTransactionOpen(false);
            setSelectedAssetForAddTransaction(null);
          }}
          onAddTransaction={handleAddTransaction}
          asset={selectedAssetForAddTransaction}
        />
      )}
    </>
  );
}
