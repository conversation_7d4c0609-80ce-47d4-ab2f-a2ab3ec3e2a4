import React, { useState, useEffect } from 'react';
import { Helmet } from 'react-helmet';
import { motion, AnimatePresence } from 'framer-motion';
import { PortfolioSelector } from '@/components/portfolio/PortfolioSelector';

import { PortfolioSummary } from '@/components/portfolio/PortfolioSummary';

interface MetricDetail {
  name: string;
  percentage: number;
  score: number;
  status: string;
  description: string;
  importance: string;
  keyPoints: string[];
}
import { PortfolioAssetsList } from '@/components/portfolio/PortfolioAssetsList';
import { PortfolioPerformanceChart } from '@/components/portfolio/PortfolioPerformanceChart';
import { PortfolioAllocationChart } from '@/components/portfolio/PortfolioAllocationChart';
import { PortfolioMetricsRadar } from '@/components/portfolio/PortfolioMetricsRadar';
import { PortfolioHealthMetric } from '../components/PortfolioHealthMetric';
// API'den gerçek veriler alındığı için mock veriler kaldırıldı
import { Portfolio, PortfolioAsset } from '@/lib/types';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  Download, 
  Share2, 
  TrendingUp, 
  Wallet, 
  PlusCircle, 
  Bell, 
  BarChart3, 
  RefreshCw,
  ArrowLeftRight,
  ArrowRight,
  Gift,
  HeartPulse,
  LineChart,
  BarChart
} from 'lucide-react';

// Animation variants for page transitions
const pageVariants = {
  initial: { opacity: 0, y: 10 },
  animate: { opacity: 1, y: 0 },
  exit: { opacity: 0, y: -10 }
};

export default function PortfolioAnalysis() {
  const [selectedPortfolioId, setSelectedPortfolioId] = useState('');
  const [isLoading, setIsLoading] = useState(true);
  const [portfolios, setPortfolios] = useState<Portfolio[]>([]);
  const [selectedPortfolio, setSelectedPortfolio] = useState<Portfolio | null>(null);
  
  // API'den portfolyo verilerini al
  useEffect(() => {
    // Burada gerçek API çağrısı yapılacak
    const fetchPortfolios = async () => {
      try {
        // Gerçek API verilerini al
        // const data = await portfolioService.getPortfolios();
        // setPortfolios(data);
        // if (data.length > 0) {
        //   setSelectedPortfolioId(data[0].id);
        //   setSelectedPortfolio(data[0]);
        // }
        setIsLoading(false);
      } catch (error) {
        console.error("Error fetching portfolios:", error);
        setIsLoading(false);
      }
    };
    
    fetchPortfolios();
  }, []);

  // Load new portfolio data without clearing current data during transition
  useEffect(() => {
    if (selectedPortfolioId) {
      setIsLoading(true);
      // DON'T clear current portfolio - keep it visible during loading
      const timer = setTimeout(() => {
        // In real implementation, this would be the API call result
        const newPortfolio = portfolios.find(p => p.id === selectedPortfolioId);
        setSelectedPortfolio(newPortfolio || null);
        setIsLoading(false);
      }, 300);
      return () => clearTimeout(timer);
    }
  }, [selectedPortfolioId, portfolios]);

  // Handle asset click
  const handleAssetClick = (asset: PortfolioAsset) => {
    // Navigate to asset details
    window.location.href = `/coin/${asset.id}`;
  };

  // Handle create alert
  const handleCreateAlert = (asset: PortfolioAsset) => {
    console.log('Creating alert for', asset.name);
    // Open alert creation modal/page
  };

  // Handle portfolio management actions
  const handleCreatePortfolio = () => {
    console.log('Creating new portfolio');
    // Open portfolio creation modal/page
  };

  const handleEditPortfolio = (id: string) => {
    console.log('Editing portfolio', id);
    // Open portfolio edit modal/page
  };

  const handleDeletePortfolio = (id: string) => {
    console.log('Deleting portfolio', id);
    // Open confirmation dialog
  };

  return (
    <>
      <Helmet>
        <title>Portfolio Analysis | CoinScout</title>
      </Helmet>

      <div className="p-4 md:p-6 space-y-6 max-w-[1920px] mx-auto">
        {/* Page container with border - exactly matches Coin Detail Page */}
        <div className="bg-slate-900/80 border border-slate-800 shadow-lg rounded-xl p-4 md:p-6 space-y-6 backdrop-blur-sm">
          {/* Common header for all views */}
          <div className="p-4 pb-4 border-b border-slate-800/30 bg-slate-800/30 backdrop-blur-sm flex flex-col sm:flex-row sm:items-center justify-between gap-4 -mx-6 -mt-6 mb-6 rounded-t-xl">
            <div>
              <h1 className="text-2xl font-bold text-white mb-1">Portfolio Analysis</h1>
              <p className="text-slate-400">Analyze your portfolio performance and health</p>
            </div>

            <div className="flex items-center gap-3">
              <PortfolioSelector
                portfolios={portfolios}
                selectedPortfolioId={selectedPortfolioId}
                onSelectPortfolio={setSelectedPortfolioId}
                onCreatePortfolio={handleCreatePortfolio}
                onEditPortfolio={handleEditPortfolio}
                onDeletePortfolio={handleDeletePortfolio}
                variant="dropdown"
              />

              <Button 
                variant="outline" 
                className="bg-slate-800/60 border-slate-700/40 text-slate-200 hover:bg-slate-800/80 shadow-md hover:shadow-lg transition-all duration-200"
                onClick={() => selectedPortfolio && window.open(`/portfolio/${selectedPortfolio.id}/export`, '_blank')}
                disabled={!selectedPortfolio}
              >
                <Download className="h-4 w-4 mr-1" />
                Export
              </Button>

              <Button 
                variant="outline" 
                className="bg-slate-800/60 border-slate-700/40 text-slate-200 hover:bg-slate-800/80 shadow-md hover:shadow-lg transition-all duration-200"
                onClick={() => selectedPortfolio && window.open(`/portfolio/${selectedPortfolio.id}/share`, '_blank')}
                disabled={!selectedPortfolio}
              >
                <Share2 className="h-4 w-4 mr-1" />
                Share
              </Button>
            </div>
          </div>

          {/* Professional view content */}
          <AnimatePresence mode="wait">
            {isLoading && !selectedPortfolio ? (
              <motion.div
                key="loading"
                initial="initial"
                animate="animate"
                exit="exit"
                variants={pageVariants}
                transition={{ duration: 0.3 }}
                className="min-h-[600px] flex items-center justify-center"
              >
                <div className="space-y-4 text-center">
                  <div className="w-16 h-16 border-4 border-[#00E4FF] border-t-transparent rounded-full animate-spin mx-auto" />
                  <div className="text-[#66B2FF]">Loading view...</div>
                </div>
              </motion.div>
            ) : (
              <motion.div
                key="professional"
                initial="initial"
                animate="animate"
                exit="exit"
                variants={pageVariants}
                transition={{ duration: 0.3 }}
                className="relative"
              >
                {/* Loading overlay for portfolio transitions */}
                {isLoading && selectedPortfolio && (
                  <div className="absolute top-0 left-0 right-0 z-10 bg-slate-900/60 backdrop-blur-sm border border-slate-700/40 rounded-lg p-3 mb-4">
                    <div className="flex items-center gap-3">
                      <div className="w-4 h-4 border-2 border-[#00E4FF] border-t-transparent rounded-full animate-spin" />
                      <span className="text-slate-200 text-sm">Loading new portfolio data...</span>
                    </div>
                  </div>
                )}

                {/* Professional view content */}
                <div className={isLoading && selectedPortfolio ? "pt-16" : ""}>
                  {/* Top metrics row */}
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div className="bg-gradient-to-br from-slate-800/95 to-slate-900/95 border border-slate-700/20 shadow-md rounded-xl overflow-hidden">
                      <div className="p-4 pb-4 border-b border-slate-800/30 bg-slate-800/30 backdrop-blur-sm">
                        <div className="flex items-center gap-2">
                          <div className="bg-blue-500/10 p-1.5 rounded-md">
                            <Wallet className="h-4 w-4 text-blue-400" />
                          </div>
                          <h3 className="text-white font-medium">Portfolio Summary</h3>
                        </div>
                      </div>
                      <div className="p-4">
                        {selectedPortfolio ? (
                          <PortfolioSummary portfolio={selectedPortfolio} variant="default" />
                        ) : (
                          <div className="h-[120px] flex items-center justify-center">
                            <div className="text-center space-y-3">
                              <div className="w-8 h-8 border-4 border-[#00E4FF] border-t-transparent rounded-full animate-spin mx-auto" />
                              <div className="text-slate-400 text-sm">Loading summary...</div>
                            </div>
                          </div>
                        )}
                      </div>
                    </div>

                    <div className="bg-gradient-to-br from-slate-800/95 to-slate-900/95 border border-slate-700/20 shadow-md rounded-xl overflow-hidden">
                      <div className="p-4 pb-4 border-b border-slate-800/30 bg-slate-800/30 backdrop-blur-sm">
                        <div className="flex items-center gap-2">
                          <div className="bg-green-500/10 p-1.5 rounded-md">
                            <BarChart className="h-4 w-4 text-green-400" />
                          </div>
                          <h3 className="text-white font-medium">Portfolio Metrics</h3>
                        </div>
                      </div>
                      <div className="p-4">
                        {selectedPortfolio ? (
                          <PortfolioMetricsRadar portfolio={selectedPortfolio} minimal={true} className="h-full" />
                        ) : (
                          <div className="h-[180px] flex items-center justify-center">
                            <div className="text-center space-y-3">
                              <div className="w-8 h-8 border-4 border-[#00E4FF] border-t-transparent rounded-full animate-spin mx-auto" />
                              <div className="text-slate-400 text-sm">Loading metrics...</div>
                            </div>
                          </div>
                        )}
                      </div>
                    </div>

                    <div className="bg-gradient-to-br from-slate-800/95 to-slate-900/95 border border-slate-700/20 shadow-md rounded-xl overflow-hidden">
                      <div className="p-4 pb-4 border-b border-slate-800/30 bg-slate-800/30 backdrop-blur-sm">
                        <div className="flex items-center gap-2">
                          <div className="bg-purple-500/10 p-1.5 rounded-md">
                            <ArrowRight className="h-4 w-4 text-purple-400" />
                          </div>
                          <h3 className="text-white font-medium">Quick Actions</h3>
                        </div>
                      </div>
                      <div className="p-4 grid grid-cols-2 gap-3">
                        <Button 
                          variant="outline" 
                          className="bg-slate-800/60 border-slate-700/40 text-slate-200 hover:bg-slate-800/80 hover:text-slate-100 shadow-md hover:shadow-lg transition-all duration-200 justify-start"
                          onClick={() => window.open('/portfolio/add-asset', '_blank')}
                        >
                          <PlusCircle className="h-4 w-4 mr-2 text-blue-400" />
                          Add Asset
                        </Button>
                        <Button 
                          variant="outline" 
                          className="bg-slate-800/60 border-slate-700/40 text-slate-200 hover:bg-slate-800/80 hover:text-slate-100 shadow-md hover:shadow-lg transition-all duration-200 justify-start"
                          onClick={() => window.open('/alerts/create', '_blank')}
                        >
                          <Bell className="h-4 w-4 mr-2 text-amber-400" />
                          Create Alert
                        </Button>
                        <Button 
                          variant="outline" 
                          className="bg-slate-800/60 border-slate-700/40 text-slate-200 hover:bg-slate-800/80 hover:text-slate-100 shadow-md hover:shadow-lg transition-all duration-200 justify-start"
                          onClick={() => window.open('/portfolio/rebalance', '_blank')}
                        >
                          <ArrowLeftRight className="h-4 w-4 mr-2 text-purple-400" />
                          Rebalance
                        </Button>
                        <Button 
                          variant="outline" 
                          className="bg-slate-800/60 border-slate-700/40 text-slate-200 hover:bg-slate-800/80 hover:text-slate-100 shadow-md hover:shadow-lg transition-all duration-200 justify-start"
                          onClick={() => window.open('/portfolio/analysis', '_blank')}
                        >
                          <BarChart3 className="h-4 w-4 mr-2 text-teal-400" />
                          Full Analysis
                        </Button>
                      </div>

                      <div className="p-4 border-t border-slate-800/30">
                        <div className="bg-slate-800/40 border border-slate-700/30 rounded-xl py-3 px-4 flex items-start gap-3 shadow-md">
                          <div className="bg-indigo-900/30 p-1.5 rounded-md">
                            <Gift className="h-4 w-4 text-indigo-300" />
                          </div>
                          <div>
                            <h4 className="text-white font-medium text-sm mb-1">AI Portfolio Suggestion</h4>
                            <p className="text-slate-400 text-xs mb-2">Improve your portfolio score by rebalancing with AI recommendations</p>
                            <div className="flex items-center justify-between">
                              <Badge className="bg-indigo-900/30 text-indigo-200 border border-indigo-800/40">+7.5 Score Potential</Badge>
                              <Button 
                                size="sm" 
                                variant="ghost" 
                                className="h-7 text-slate-200 hover:bg-slate-700/50 transition-all duration-200"
                                onClick={() => window.open('/portfolio/ai-suggestions', '_blank')}
                              >
                                View <ArrowRight className="h-3 w-3 ml-1" />
                              </Button>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Main content area */}
                  <div className="flex flex-col gap-4 mt-4">
                    {/* First section with Performance Chart and Assets */}
                    <div className="grid grid-cols-1 lg:grid-cols-3 gap-4">
                      <div className="lg:col-span-3 space-y-4">
                        {selectedPortfolio ? (
                          <PortfolioPerformanceChart 
                            portfolio={selectedPortfolio} 
                            timeRange="1M" 
                          />
                        ) : (
                          <div className="h-[300px] flex items-center justify-center bg-gradient-to-br from-slate-800/95 to-slate-900/95 border border-slate-700/20 shadow-md rounded-xl">
                            <div className="text-center space-y-3">
                              <div className="w-12 h-12 border-4 border-[#00E4FF] border-t-transparent rounded-full animate-spin mx-auto" />
                              <div className="text-slate-400">Loading performance chart...</div>
                            </div>
                          </div>
                        )}

                        <div className="bg-gradient-to-br from-slate-800/95 to-slate-900/95 border border-slate-700/20 shadow-md rounded-xl overflow-hidden">
                          <Tabs defaultValue="assets" className="w-full">
                            <TabsList className="w-full bg-slate-800/30 mb-0 px-4 pt-4 pb-0 border-b border-slate-800/30 backdrop-blur-sm">
                              <TabsTrigger 
                                value="assets" 
                                className="flex-1 data-[state=active]:bg-slate-700/60 data-[state=active]:text-white data-[state=active]:shadow-md"
                              >
                                Assets
                              </TabsTrigger>
                              <TabsTrigger 
                                value="transactions" 
                                className="flex-1 data-[state=active]:bg-slate-700/60 data-[state=active]:text-white data-[state=active]:shadow-md"
                              >
                                Transactions
                              </TabsTrigger>
                              <TabsTrigger 
                                value="history" 
                                className="flex-1 data-[state=active]:bg-slate-700/60 data-[state=active]:text-white data-[state=active]:shadow-md"
                              >
                                Performance History
                              </TabsTrigger>
                            </TabsList>

                            <TabsContent value="assets" className="mt-0 p-4 w-full">
                              {selectedPortfolio ? (
                                <PortfolioAssetsList 
                                  portfolio={selectedPortfolio} 
                                  onAssetClick={handleAssetClick}
                                  onCreateAlert={handleCreateAlert}
                                  variant="detailed"
                                  className="w-full"
                                />
                              ) : (
                                <div className="h-[300px] flex items-center justify-center">
                                  <div className="text-center space-y-3">
                                    <div className="w-12 h-12 border-4 border-[#00E4FF] border-t-transparent rounded-full animate-spin mx-auto" />
                                    <div className="text-slate-400">Loading assets...</div>
                                  </div>
                                </div>
                              )}
                            </TabsContent>

                            <TabsContent value="transactions" className="mt-0">
                              <div className="h-[400px] p-4 flex items-center justify-center">
                                <div className="text-center">
                                  <div className="w-12 h-12 bg-gradient-to-br from-indigo-900/60 to-indigo-900/40 flex items-center justify-center rounded-full mx-auto mb-3 shadow-inner border border-indigo-800/30">
                                    <TrendingUp className="h-6 w-6 text-indigo-400/90" />
                                  </div>
                                  <h3 className="text-white font-medium mb-1">Transaction History</h3>
                                  <p className="text-slate-400 max-w-md mb-4">Track all your portfolio transactions, including buys, sells, swaps, and transfers</p>
                                  <Button
                                    className="bg-gradient-to-r from-indigo-500 to-indigo-600 text-white hover:from-indigo-600 hover:to-indigo-700 transition-all duration-200 shadow-md hover:shadow-lg"
                                    onClick={() => window.open('/portfolio/transactions/add', '_blank')}
                                  >
                                    <PlusCircle className="h-4 w-4 mr-2" />
                                    Add Transaction
                                  </Button>
                                </div>
                              </div>
                            </TabsContent>

                            <TabsContent value="history" className="mt-0">
                              <div className="h-[400px] p-4 flex items-center justify-center">
                                <div className="text-center">
                                  <div className="w-12 h-12 bg-gradient-to-br from-blue-900/60 to-blue-900/40 flex items-center justify-center rounded-full mx-auto mb-3 shadow-inner border border-blue-800/30">
                                    <Wallet className="h-6 w-6 text-blue-400/90" />
                                  </div>
                                  <h3 className="text-white font-medium mb-1">Performance History</h3>
                                  <p className="text-slate-400 max-w-md mb-4">View detailed historical data about your portfolio performance over time</p>
                                  <Button
                                    className="bg-gradient-to-r from-blue-500 to-blue-600 text-white hover:from-blue-600 hover:to-blue-700 transition-all duration-200 shadow-md hover:shadow-lg"
                                    onClick={() => window.open('/portfolio/performance', '_blank')}
                                  >
                                    <BarChart3 className="h-4 w-4 mr-2" />
                                    View Detailed Analysis
                                  </Button>
                                </div>
                              </div>
                            </TabsContent>
                          </Tabs>
                        </div>
                      </div>
                    </div>

                    {/* Second section with 50-50 grid for Portfolio Allocation and Portfolio Health */}
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-2">
                      <div className="bg-gradient-to-br from-slate-800/95 to-slate-900/95 border border-slate-700/20 shadow-md rounded-xl overflow-hidden">
                        <div className="p-4 pb-4 border-b border-slate-800/30 bg-slate-800/30 backdrop-blur-sm flex justify-between items-center">
                          <div className="flex items-center gap-2">
                            <div className="bg-purple-500/10 p-1.5 rounded-md">
                              <BarChart3 className="h-4 w-4 text-purple-400" />
                            </div>
                            <h3 className="text-white font-medium">Portfolio Allocation</h3>
                          </div>
                          
                        </div>
                        <div className="p-4">
                          {selectedPortfolio ? (
                            <PortfolioAllocationChart portfolio={selectedPortfolio} />
                          ) : (
                            <div className="h-[300px] flex items-center justify-center">
                              <div className="text-center space-y-3">
                                <div className="w-12 h-12 border-4 border-[#00E4FF] border-t-transparent rounded-full animate-spin mx-auto" />
                                <div className="text-slate-400">Loading allocation data...</div>
                              </div>
                            </div>
                          )}
                        </div>
                      </div>

                      <div className="bg-gradient-to-br from-slate-800/95 to-slate-900/95 border border-slate-700/20 shadow-md rounded-xl overflow-hidden">
                        {/* Using the PortfolioHealthMetric component with embedded header */}
                        <div className="max-h-[600px] overflow-hidden">
                          {selectedPortfolio ? (
                            <PortfolioHealthMetric 
                              score={selectedPortfolio.metrics.totalScore}
                              metrics={Object.entries(selectedPortfolio.metrics)
                              .filter(([key]) => key !== 'totalScore')
                              .map(([key, metric]) => {
                                const metricName = key.replace(/([A-Z])/g, ' $1').trim();
                                const status = getMetricStatus(metric.score);

                                // Map metric descriptions based on the key
                                interface MetricDescription {
                                  description: string;
                                  importance: string;
                                  keyPoints: string[];
                                }

                                const metricDescriptions: Record<string, MetricDescription> = {
                                  diversification: {
                                    description: "Measures how well your portfolio is spread across different cryptocurrencies and asset types.",
                                    importance: "A well-diversified portfolio reduces risk by ensuring you are not overly exposed to the performance of a single asset.",
                                    keyPoints: [
                                      "Current diversification level across assets",
                                      "Optimal number of assets recommendation",
                                      "Risk concentration analysis"
                                    ]
                                  },
                                  sectorBalance: {
                                    description: "Evaluates distribution across different cryptocurrency sectors like DeFi, NFTs, Layer 1, etc.",
                                    importance: "Sector balance helps mitigate sector-specific risks while maintaining exposure to high-growth areas.",
                                    keyPoints: [
                                      "Distribution across major crypto sectors",
                                      "Over/under-exposure to specific sectors",
                                      "Sector correlation analysis"
                                    ]
                                  },
                                  marketCapDistribution: {
                                    description: "Analyzes your allocation across large, mid, and small-cap cryptocurrencies.",
                                    importance: "A balanced market cap distribution combines stability of established projects with growth potential of smaller ones.",
                                    keyPoints: [
                                      "Large cap exposure (safety)",
                                      "Mid cap allocation (balanced growth)",
                                      "Small cap percentage (high growth potential)"
                                    ]
                                  },
                                  investmentBalance: {
                                    description: "Measures how evenly your funds are distributed across your portfolio assets.",
                                    importance: "Proper investment balance prevents over-concentration in specific assets that could lead to disproportionate risk.",
                                    keyPoints: [
                                      "Position size analysis",
                                      "Concentration risk assessment",
                                      "Investment distribution optimization"
                                    ]
                                  },
                                  stablecoinRatio: {
                                    description: "Evaluates the proportion of stablecoins in your portfolio as a risk management measure.",
                                    importance: "Stablecoins provide portfolio stability and dry powder for opportunities during market volatility.",
                                    keyPoints: [
                                      "Current stablecoin percentage",
                                      "Recommended stablecoin allocation",
                                      "Volatility protection assessment"
                                    ]
                                  },
                                  projectQuality: {
                                    description: "Assesses the fundamental quality of projects in your portfolio based on multiple factors.",
                                    importance: "High-quality projects tend to perform better long-term and survive market downturns.",
                                    keyPoints: [
                                      "Team experience and track record",
                                      "Technological innovation and adoption",
                                      "Community engagement and development activity"
                                    ]
                                  }
                                };

                                const defaultDescription = {
                                  description: "Analysis of your portfolio's performance in this metric category.",
                                  importance: "This metric contributes to your overall portfolio health assessment.",
                                  keyPoints: [
                                    "Metric evaluation based on industry standards",
                                    "Comparative analysis against benchmark",
                                    "Optimization recommendations"
                                  ]
                                };

                                const descriptionData = metricDescriptions[key.toLowerCase()] || defaultDescription;

                                return {
                                  name: metricName,
                                  percentage: Math.round(metric.score),
                                  score: Math.round(metric.score),
                                  status: status.label,
                                  ...descriptionData
                                };
                              })
                            }
                          />
                          ) : (
                            <div className="h-[400px] flex items-center justify-center">
                              <div className="text-center space-y-3">
                                <div className="w-12 h-12 border-4 border-[#00E4FF] border-t-transparent rounded-full animate-spin mx-auto" />
                                <div className="text-slate-400">Loading health metrics...</div>
                              </div>
                            </div>
                          )}
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </motion.div>
            )}
          </AnimatePresence>
        </div>
      </div>
    </>
  );
}

// Helper function to get metric status
function getMetricStatus(score: number) {
  if (score >= 90) return { label: 'Excellent', color: 'text-green-400 border-green-400/30' };
  if (score >= 80) return { label: 'Good', color: 'text-blue-400 border-blue-400/30' };
  if (score >= 70) return { label: 'Fair', color: 'text-yellow-400 border-yellow-400/30' };
  if (score >= 60) return { label: 'Poor', color: 'text-orange-400 border-orange-400/30' };
  return { label: 'Bad', color: 'text-red-400 border-red-400/30' };
}