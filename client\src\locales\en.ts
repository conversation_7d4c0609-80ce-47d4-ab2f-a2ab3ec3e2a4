/**
 * English localization strings
 */

export default {
  // FAQ translations
  faq: {
    navigation: {
      backToHome: "Back to Home",
      helpCenter: "Help Center",
    },
    header: {
      title: "Frequently Asked Questions",
      subtitle: "Find answers to common questions about CoinScout's features and services.",
    },
    support: {
      title: "Can't find what you're looking for?",
      description: "Our support team is here to help! Contact us at",
      email: "<EMAIL>",
    },
    sections: {
      general: {
        title: "General Questions",
        description: "Basic information about CoinScout",
        questions: {
          whatIs: {
            question: "What is CoinScout?",
            answer: "CoinScout is a data-driven cryptocurrency analysis platform that helps investors discover and evaluate projects using real-time metrics. Our platform aggregates data from third-party sources and uses proprietary scoring algorithms to provide insights into project fundamentals, tokenomics, security, market, and social metrics.",
          },
          howWorks: {
            question: "How does CoinScout work?",
            answer: "CoinScout collects data from trusted third-party APIs (such as CryptoRank, CoinGecko, CoinMarketCap, CertiK, Lunar Crush) and evaluates cryptocurrency projects across multiple categories. We provide comprehensive scores based on tokenomics, fundraising, security audits, social media metrics, and more, helping you make informed investment decisions quickly and efficiently.",
          },
          dataTypes: {
            question: "What type of data does CoinScout use to score projects?",
            answer: {
              intro: "We source data from reputable platforms, including:",
              items: [
                "Market data (e.g., market cap, trading volume, liquidity)",
                "Tokenomics (e.g., supply, vesting schedules, allocations)",
                "Social metrics (e.g., follower counts, social sentiment)",
                "Security audits (e.g., CertiK audits)",
                "Fundraising and project milestones",
              ],
            },
          },
          whyLimited: {
            question: "Why does CoinScout only list 2,000-3,000 cryptocurrencies while other platforms have tens of thousands?",
            answer: {
              intro: "CoinScout focuses on highlighting only projects with real potential rather than overwhelming users with thousands of irrelevant coins. Our expert team has developed a complex and highly selective algorithm that filters out low-quality projects. Here are some of our key criteria:",
              items: [
                "Coins with a market cap below $1 million are not listed.",
                "Coins scoring below our rating threshold are excluded.",
                "Assets like stablecoins and wrapped tokens, as well as speculative assets like meme coins, are not included.",
              ],
              conclusion: "Currently, only around 2,000-3,000 coins meet these criteria, and this number may fluctuate based on market conditions.",
            },
          },
          aiDriven: {
            question: "Is CoinScout fully AI-driven, or are there other factors involved?",
            answer: {
              intro: "CoinScout is not just powered by AI—it integrates multiple advanced technologies and expert insights to provide the most accurate crypto investment analysis. Our system is built on:",
              items: [
                "Machine Learning (ML): Continuously improving predictive models based on real-time data.",
                "Data Analysis: Deep fundamental and sentiment analysis to filter high-potential projects.",
                "Experienced Crypto Experts: Human expertise ensures AI-driven insights remain relevant, accurate, and actionable.",
              ],
              conclusion: "By combining AI, ML, data analytics, and human intelligence, CoinScout provides a balanced and reliable investment evaluation system.",
            },
          },
        },
      },
      technical: {
        title: "Technical and Security",
        description: "Security and technical details",
        questions: {
          dataAccuracy: {
            question: "How do you ensure the accuracy of the data?",
            answer: "We aggregate data from reputable sources like CoinGecko, CoinMarketCap, and CertiK, which are known for their reliability. While we strive to ensure data accuracy, discrepancies may occur due to third-party data limitations. We recommend that users cross-reference data where necessary.",
          },
          securityAudits: {
            question: "Does CoinScout perform security audits on projects?",
            answer: "No, CoinScout does not directly perform security audits. Instead, we aggregate audit data from established auditing firms like CertiK, Hacken, and others. Our platform reflects whether a project has been audited and the results of those audits where available.",
          },
        },
      },
      scoring: {
        title: "Scoring and Metrics",
        description: "Understanding our scoring system",
        questions: {
          marketCapRatio: {
            question: "What is the Market Cap / FDV Ratio, and why is it important?",
            answer: "The Market Cap to FDV (Fully Diluted Valuation) Ratio is a metric that shows the relationship between a project's current market cap and its fully diluted valuation. It helps investors understand how much of the total token supply is currently in circulation and whether future token releases may dilute their holdings.",
          },
          tokenomicsScore: {
            question: "How does the platform score tokenomics?",
            answer: "Tokenomics scores are based on various factors, such as total supply, circulating supply, vesting schedules, and team/investor allocations. Projects with fair token distribution, longer vesting schedules, and limited token inflation tend to score higher.",
          },
          memecoins: {
            question: "Why doesn't CoinScout include memecoins?",
            answer: {
              intro: "Memecoins are classified as highly speculative assets, which means they are generally not suitable for serious investors. Here's why we don't include them:",
              items: [
                "Statistically, most memecoin investors lose money, with only a few insiders and lucky traders making profits.",
                "The memecoin market operates similarly to gambling, favoring early movers while leaving late investors at a disadvantage.",
                "CoinScout's experienced investment team believes it is unethical to promote highly speculative assets to our users.",
              ],
              conclusion: "For these reasons, memecoins do not align with our mission of providing reliable, long-term investment insights.",
            },
          },
        },
      },
      account: {
        title: "Account and Subscription",
        description: "Managing your account",
        questions: {
          createAccount: {
            question: "How do I create an account on CoinScout?",
            answer: "You can sign up for a free or premium account by clicking the \"Sign Up\" button on our homepage. Simply enter your email address, choose a password, and follow the prompts to get started.",
          },
          premiumFeatures: {
            question: "What is included in the premium subscription?",
            answer: {
              intro: "Premium users have access to:",
              items: [
                "Advanced scoring metrics and data",
                "Deeper insights into tokenomics, social metrics, and market activity",
                "Personalized investment reports based on user preferences",
                "Priority support and early access to new features",
              ],
            },
          },
        },
      },
    },
  },

  // Coin list related translations
  coinlist: {
    title: "All Coins",
    search: "Search coins...",
    filters: {
      title: "Filter Options",
      description: "Customize your view with advanced filtering options",
      button: "Filters",
    },
    viewAll: "View All",
    coinhealthscore: "Coin Health Score",
    headers: {
      name: "Name",
      tokenomics: "Tokenomics",
      security: "Security",
      social: "Social",
      market: "Market",
      insights: "Insights",
      totalAIScore: "Total AI Score",
      "7dChange": "7d Change",
    },
    tooltips: {
      name: {
        title: "Coin Name & Symbol",
        description:
          "The official name and symbol of the cryptocurrency as listed on exchanges.",
      },
      tokenomics: {
        title: "Tokenomics Analysis",
        description:
          "Measures token supply mechanisms, inflation risks, and vesting structures.",
      },
      security: {
        title: "Security Analysis",
        description: "Security audit results and risk assessment metrics.",
      },
      social: {
        title: "Social Analysis",
        description:
          "Social media presence, community engagement and sentiment analysis.",
      },
      market: {
        title: "Market Performance Analysis",
        description:
          "Measures trading volume, liquidity and overall market health.",
      },
      insights: {
        title: "AI Insights Analysis",
        description:
          "AI-powered project insights, sentiment analysis and forecasting metrics.",
      },
      totalScore: {
        title: "Total Score",
        description: "The overall rating calculated from all scoring metrics.",
      },
      sevenDayChange: {
        title: "7 Day Score Change",
        description: "Score change over the last 7 days.",
      },
    },
  },

  session: {
    expired: {
      title: "Session expired",
      description: "Your session has expired. Please log in again.",
    },
  },

  // Hover card translations
  hoverCard: {
    marketCapRank: "Market Cap Rank",
    currentPrice: "Current Price",
    marketCap: "Market Cap",
    watchlistCount: "Watchlist Count",
    usersWatching: "users watching",
    loadingText: "Loading information...",
    rankLabel: "#",
    "1day": "1D",
    "1week": "1W",
    "1month": "1M",
    "3months": "3M",
    "1year": "1Y",
  },

  // Calculation data display translations
  calculationData: {
    // Metric names
    metrics: {
      m1: "Market Cap / FDV Ratio",
      m2: "Max Supply",
      m3: "Code Security",
      m4: "Community Trust",
      m5: "Total Trade Volume",
      m6: "Best CEX Rank",
      m7: "Best DEX Rank",
      m8: "CEX Count",
      m9: "DEX Count",
      m11: "Vesting Schedule",
      m12: "Emission Score (1Y)",
      m14: "Risk/Reward Rating",
      m17: "Fundamental Health",
      m18: "Governance Strength",
      m19: "Market Stability",
      m20: "Operational Resilience",
      m23: "Galaxy Score",
      m24: "Alt Rank",
      m27: "Sentiment",
      m28: "Gecko Portfolio Count",
      m29: "Mentions Score",
      m30: "Social Dominance",
      default: "Metric Value",
    },
    // UI terms
    ui: {
      overview: "Overview",
      summary: "Summary",
      score: "Score",
      status: "Status",
      mechanisms: "Mechanisms",
      total: "Total",
      yes: "Yes",
      no: "No",
      deflationary: "Deflationary",
      redistribution: "Redistribution",
      buyback: "Buyback",
      revenue: "Revenue",
      sharing: "Sharing",
      totalUseCases: "Total Use Cases",
      totalMechanisms: "Total Mechanisms",
      burnMechanisms: "Burn Mechanisms",
      buybackMechanisms: "Buyback Mechanisms",
      stakingMechanisms: "Staking",
      feeSharingMechanisms: "Fee Sharing",
      rewardMechanisms: "Rewards",
      revenueBuybacks: "Revenue Buybacks",
      burnBuybacks: "Burn Buybacks",
      hasBurnComponent: "Has Burn Component",
      positiveMechanisms: "Positive",
      negativeMechanisms: "Negative",
      governanceFeatures: "Governance Features",
      whitepaperExtract:
        "🤖The relevant section extracted from the project's whitepaper.",
      revenueSharing: "Revenue Sharing",
      features: "Features",
      feeDistribution: "Fee Distribution",
      passiveIncome: "Passive Income",
      profitSharing: "Profit Sharing",
      stakingRequired: "Staking Required",
      teamStatus: "Team Status",
      totalTeamMembers: "Total Team Members",
      governance: "Governance",
      tokenVoting: "Token Voting",
      decentralized: "Decentralized",
      teamAnonymousOrPublic: "Team Anonymous or Public",
      daoGovernance: "DAO Governance",
      tokenUseCase: "Token Use Case",
      deflationaryOrInflationary: "Deflationary or Inflationary Token",
      tokenRedistribution: "Token Redistribution",
      buybackMechanism: "Buyback Mechanism",
      calculationData: "Calculation Data",
      notAvailable: "N/A",
      seeAll: "See All",
      seeLess: "See Less",
    },
  },

  // Compare page translations
  compare: {
    insigths: "Insights",
    tokenomics: "Tokenomics",
    security: "Security",
    socials: "Social",
    market: "Market",
    title: "Compare Cryptocurrencies",
    overview: "Overview",
    metrics: "Metrics",
    categorySelection: "Category Selection",
    gettingStarted: "Getting Started",
    selectInstruction:
      "Select up to 4 cryptocurrencies to compare their performance metrics and identify the strongest investment options.",
    selectCoin: "Select coin",
    bestPerformer: "Best Performer",
    overallScore: "Overall Score",
    noCoinsSelected: "No coins selected for comparison",
    selectCoinsForBest: "Please select coins to see the best performer",
    selectCoinsForMetrics: "Please select coins to view metrics",
    searching: "Searching...",
    noResults: "No results found",
    addtext: "Type at least 2 characters to search",
    metric: "Metric",
    winner: "Winner",
    price: "Price",
    marketCap: "Market Cap",
    overallRating: "Overall Rating",
    viewDetailedAnalysis: "View Detailed Analysis",
    scoreFor: "score for",
    vs: "vs",
    insight: "Insight",
    selectionStatus: "selected",
    resetSelection: "Reset selection",
    max: "Max",
    oneLeft: "1 left",
    noData: "No data",
    noWinner: "No winner",
  },

  // Pagination strings
  pagination: {
    showing: "Showing",
    of: "of",
    rows: "rows",
  },

  // Authentication namespace with colon-separated keys
  "auth:fields.password": "Password",
  "auth:forgotPassword": "Forgot Password?",
  "auth:remember": "Remember me for 30 days",
  "auth:signin.securely": "Sign in securely",
  "auth:signin.loading": "Signing in...",
  "auth:continueWith": "Or continue with",
  "auth:termsAccept": "By clicking continue, you agree to our",
  "auth:terms.service": "Terms of Service",
  "auth:terms.privacy": "Privacy Policy",
  "auth:terms.and": "and",
  "auth:resetPassword": "Reset Your Password",
  "auth:backToLogin": "Back to Login",
  "auth:backToUpcoming": "Back to Upcoming",
  "auth:email": "Email",
  "auth:password": "Password",
  "auth:password.strength": "Password Strength",
  "auth:password.strength.weak": "Weak",
  "auth:password.strength.good": "Good",
  "auth:password.strength.strong": "Strong",
  "auth:login": "Login",
  "auth:register": "Register",
  "auth:authentication.signInPrompt":
    "Sign in to access personalized features, save your preferences, and unlock the full capabilities of CoinScout.",
  "auth:authentication.signin": "Sign in",
  "auth:authentication.continueWithEmail": "Continue with email",
  "auth:authentication.goBack": "Go back",
  "auth:login.title": "CoinScout Login",
  "auth:register.title": "Create Account",

  // Password validation
  "auth:password.criteria.length": "At least 8 characters",
  "auth:password.criteria.uppercase": "At least one uppercase letter",
  "auth:password.criteria.lowercase": "At least one lowercase letter",
  "auth:password.criteria.number": "At least one number",
  "auth:password.criteria.special": "At least one special character",

  // Common auth validation
  "common:auth:validation.username.min":
    "Username must be at least 3 characters",
  "common:auth:validation.email.invalid": "Please enter a valid email address",
  "common:auth:validation.password.min":
    "Password must be at least 8 characters",
  "common:auth:validation.password.uppercase":
    "Must contain at least one uppercase letter",
  "common:auth:validation.password.lowercase":
    "Must contain at least one lowercase letter",
  "common:auth:validation.password.number": "Must contain at least one number",
  "common:auth:validation.password.special":
    "Must contain at least one special character",
  "common:auth:validation.terms": "You must accept the terms of service",
  "common:auth:validation.password.match": "Passwords do not match",

  // Register form fields
  "common:auth:username": "Username",
  "common:auth:username.placeholder": "Choose a username",
  "common:auth:email.placeholder": "Enter your email",
  "common:auth:email.description":
    "We'll never share your email with anyone else",
  "common:auth:password.create": "Create a password",
  "common:auth:password.show": "Show password",

  "common:auth:password.confirm": "Confirm Password",
  "common:auth:password.confirm.placeholder": "Confirm your password",

  // Register form additional text
  "auth:terms.agree": "I agree to the Terms of Service and Privacy Policy",
  "auth:captcha.protected": "This form is protected by reCAPTCHA",
  "auth:register.create": "Create account",
  "auth:register.haveAccount": "Already have an account?",
  "auth:register.emailVerification.title": "Verify Your Email",
  "auth:register.emailVerification.description":
    "We've sent a verification email to your address. Please check your inbox and click the verification link to activate your account.",
  "auth:register.emailVerification.checkSpam":
    "If you don't see the email, please check your spam folder.",
  "auth:register.emailVerification.understood": "Understood",
  "auth:login.noAccount": "Don't have an account?",

  // Login toast messages
  "auth:login.error": "Login error",
  "auth:login.noResponse":
    "No response received from server. Please try again.",
  "auth:login.failed": "Login failed",
  "auth:login.invalidCredentials":
    "Authentication failed. Please check your credentials.",
  "auth:login.successful": "Login successful",
  "auth:login.welcome": "Welcome back!",
  "auth:login.genericError": "Could not log in. Please check your credentials.",
  "auth:login.unknownError": "An unknown error occurred during login",

  // Registration toast messages
  "auth:registration.error": "Registration error",
  "auth:registration.noResponse":
    "No response received from server. Please try again.",
  "auth:registration.failed": "Registration failed",
  "auth:registration.genericError": "Registration failed. Please try again.",
  "auth:registration.sessionError":
    "Account was created but session could not be established. Please log in.",
  "auth:registration.successful": "Registration successful",
  "auth:registration.emailVerification":
    "Registration successful. Please check your email to verify your account.",
  "auth:registration.couldNotRegister": "Could not register. Please try again.",
  "auth:registration.unknownError":
    "An unknown error occurred during registration",

  // Logout toast messages
  "auth:logout.successful": "Logged out",
  "auth:logout.success": "You have been successfully logged out.",
  "auth:logout.error": "Logout error",
  "auth:logout.errorDescription":
    "An error occurred during logout. Please try again.",

  // Authentication error messages
  "auth:authentication.error": "Authentication error",
  "auth:authentication.sessionError":
    "Session could not be established. Please try again.",
  "auth:notImplemented": "Not implemented",

  // Authentication prompts
  "auth:authentication.required": "Authentication Required",
  "auth:authentication.required.description":
    "Please log in to access this feature",
  "auth:authentication.comparison.required": "Authentication Required",
  "auth:authentication.comparison.description":
    "Please log in to use the comparison feature",

  // Common namespace with colon-separated keys
  "common:back.home": "Back to Home",

  // Nav strings
  "nav:home": "Home",
  "nav:coins": "Coins",
  "nav:idos": "IDOs",
  "nav:portfolio": "Portfolio",
  "nav:profile": "Profile",
  "nav:feedback": "Feedback",
  "nav:logout": "Logout",
  "nav:login": "Login",
  "nav:register": "Register",
  "nav:trending": "Trending",
  "nav:favorites": "Favorites",
  "nav:watchlist": "Watchlist",

  // Navigation colon-separated keys for compatibility
  "navigation:pricing": "Pricing",
  "navigation:goToApp": "Go to App",
  "navigation:Pricing": "Pricing",
  "navigation:Documentation": "Documentation",
  "navigation:goToHomepage": "Go to Homepage",
  "navigation:coinScoutAlt": "CoinScout - AI-Powered Crypto Analysis",
  "navigation:login": "Login",
  "navigation:signUp": "Sign Up",
  "navigation:logout": "Logout",
  "navigation:signup": "Sign Up",
  "navigation:membershipManagement": "Membership Management",

  // System colon-separated keys for compatibility
  "system:language.selector.title": "Language",
  "system:language.selector.label": "Select Language",
  "system:language.selector.available": "Available Languages",
  "system:auth.required": "Login required to view coin summary",
  "system:auth.loginButton": "Login",

  // Sidebar namespace
  sidebar: {
    home: "Home",
    coins: "Coins", 
    topMovers: "Top Movers",
    watchlist: "Watchlist",
    aiPortfolio: "AI Portfolio",
    portfolioCheckup: "Portfolio Checkup",
    compareCoins: "Compare Coins",
    upcomingIDOs: "Upcoming IDOs",
    aiAssistant: "AI Assistant",
    airdrops: "Airdrops",
    gemScout: "Gem Scout",
    soon: "Soon",
    cryptoRating: "Crypto Rating",
    cryptoRatingV2: "Crypto Rating V2",
    idoRating: "IDO Rating",
    
    // New Sidebarv2 translations
    coin_ratings: "Coin Ratings",
    ido_ratings: "IDO Ratings", 
    portfolio: "Portfolio",
    my_watchlist: "My Watchlist",
    ai_portfolio_audit: "AI Portfolio Audit",
    ai_portfolio_generator: "AI Portfolio Generator",
    alpha_tools: "Alpha Tools",
    ai_tools: "AI Tools",
    ai_assistant: "AI Assistant",
    ai_research: "AI Research", 
    compare_coins: "Compare Coins",
    top_launchpads: "Top Launchpads",
    airdrops_hub: "Airdrops Hub",
    coinscout_academy: "CoinScout Academy",
    soon: "Soon",
    lock: "Lock",
    unlock: "Unlock",
  },

  // Highlights - separate string values for each key
  topGainers: "Top Movers",
  newListings: "New Listings",
  upcomingIDOs: "Upcoming IDOs",
  score: "Score",

  // Common translations
  common: {
    searching: "Searching...",
    cancel: "Cancel",
    more: "more",
    selected: "selected",
    loading: "Loading...",
    upcomingIdos: "Upcoming IDOs",
    priceChart: {
      noDataAvailable: "No price data available",
    },
  },

  // Main coinlist translations
  aiPoweredTitle: "AI-Powered & Data-Driven Crypto Fundamental Ratings",
  highlights: "Highlights",
  nextDataUpdate: "Next Data Update",
  allCoins: "All Coins",
  coinDetailDescription: "Click on any coin for detailed analysis",
  filtersButton: "Filters",
  alertsTitle: "Alerts",
  // Table column headers
  name: "Name",
  tokenomics: "Tokenomics",
  security: "Security",
  social: "Social",
  market: "Market",
  insights: "Insights",
  totalScore: "Total Score",
  sevenDayChange: "7D Score Chg",

  // Score rating colon-separated keys for compatibility
  "score:excellent": "Excellent",
  "score:positive": "Positive",
  "score:average": "Average",
  "score:weak": "Weak",
  "score:critical": "Critical",
  "score:range": "Score Range",
  // Alerts namespace
  alerts: {
    price: "Price",
    searchCoins: "Coin Ara",
    noActiveAlerts: "You don't have any active alerts yet.",
    addText: "Type at least 2 characters to search",
    searching: "Searching...",
    noResults: "No results found",
    createFirstAlert: "Create Your First Alert",
    currentPrice: "Current Price",
    priceGoesAbove: "Price goes above",
    priceGoesBelow: "Price goes below",
    title: "Crypto Alerts",
    description: "Get notified when prices change or AI scores update",
    createNewAlert: "Create New Alert",
    activeAlerts: "Active Alerts",
    notifications: "Notifications",
    aiScore: "AI Score",
    coin: "Coin",
    priceAbove: "Price goes above",
    priceBelow: "Price goes below",
    aiScoreAbove: "Get notified when AI score exceeds your set threshold",
    aiScoreBelow: "Get notified when AI score falls below your set threshold",
    priceAboveDesc: "Get notified when price exceeds your set threshold",
    priceBelowDesc: "Get notified when price falls below your set threshold",
    selectCoin: "Select coin...",
    targetPriceAbove: "Target Price Above",
    targetPriceBelow: "Target Price Below",
    enterUpperTargetPrice: "Enter upper target price",
    enterLowerTargetPrice: "Enter lower target price",
    notificationType: "Notification Type",
    browserNotification: "Browser Notification",
    cancel: "Cancel",
    save: "Save",
    back: "Back",
    scoreGoesAbove: "Score goes above threshold",
    scoreGoesBelow: "Score goes below threshold",
    aiScoreUpperThreshold: "AI Score Upper Threshold",
    aiScoreLowerThreshold: "AI Score Lower Threshold",
  },

  marketData: {
    priceChange: "Price Change",
    priceMovement: "Price Movement",
    marketCap: "Market Cap",
    fullyDilute: "Fully Diluted",
    fdv: "FDV",
    tradeVolume24h: "24h Volume",
    marketCapDetails: "Market Cap Details",
    volumeMarketCapRatio: "Volume / Market Cap Ratio",
    marketCapRank: "Market Cap Rank",
    currentRank: "Current rank",
    updatedHourly: "Updated hourly from multiple exchanges",
  },

  // Top Movers namespace
  topMovers: {
    title: "Top Movers",
    description:
      "View cryptocurrencies with the highest upward score changes in the past 7 days",
    allCoins: "All Coins",
    clickForAnalysis: "Click on any coin for detailed analysis",
    searchPlaceholder: "Search coins...",
    filters: "Filters",
  },

  // Filters namespace
  filters: {
    title: "Filter Options",
    description: "Customize your view with advanced filtering options",
    button: "Filters",
    marketCapRange: "Market Cap Range (Million)",
    projectScoreRange: "Project Score Range",
    categories: "Categories",
    chains: "Chains",
    selectCategories: "Select categories",
    selectChains: "Select chains",
    listingDate: "Listing Date",
    chainEcosystem: "Chain / Ecosystem",
    moveSliderToAdjust: "Move the slider to adjust the range:",
    applyFilters: "Apply Filters",
    resetFilters: "Reset Filters",
    loading: "Loading...",
    noCategoriesFound: "No categories found",
    noChainsFound: "No chains found",
    cancel: "Cancel",
    loginRequired: "You need to log in first to use filters.",
    subscriptionRequired:
      "You need at least a Basic plan to use filters. Please upgrade your subscription plan.",
    login: "Login",
    upgradePlan: "Upgrade Plan",
  },

  // Newly Listed Coins namespace
  newlyListed: {
    title: "Newly Listed Coins",
    description: "Discover all newly listed cryptocurrencies",
    cardTitle: "Newly Listed Coins",
    clickForAnalysis: "Click on any coin for detailed analysis",
    searchPlaceholder: "Search coins...",
    selectTimeframe: "Select timeframe",
    last24Hours: "Last 24 Hours",
    last7Days: "Last 7 Days",
    last14Days: "Last 14 Days",
    last30Days: "Last 30 Days",
    last90Days: "Last 90 Days",
    loading: "Loading newly listed coins...",
    noCoinsFound: "No newly listed coins found.",
    filters: "Filters",
  },

  // Coin Age namespace
  coinAge: {
    comingSoon: "soon",
    comingInDays: "Coming in {days} days",
    listedToday: "Listed today",
    oneDayAgo: "1 day ago",
    daysAgo: "{days} days ago",
  },

  // Watchlist namespace
  watchlist: {
    add: "Add To Watchlist",
    addTowatchlist: "Add To Watchlist",
    addToWatchlist: "Add To Watchlist",
    inWatchlist: "In Watchlist",
    watchers: "Watchers",
    alerts: "Alerts",
    share: "Share",
    advancedView: "Advanced View",
    enable: "Enable",
    disable: "Disable",
    enableAdvancedView: "Enable advanced view",
    disableAdvancedView: "Disable advanced view",
    favorites: "Favorites",
    addToFavorites: "Add cryptocurrencies to your favorites",
    error: "Error",
    success: "Success",
    noWatchlistSelected: "No watchlist selected",
    removedFromWatchlist: "Removed from watchlist",
    coinRemovedFromWatchlist: "{coinName} removed from watchlist",
    failedToRemoveCoin: "Failed to remove coin from watchlist",
    upcomingProjectWatchlists: "Upcoming Project Watchlists",
    coinWatchlists: "Coin Watchlists",
    newList: "New List",
    selectWatchlistForProject: "Select a watchlist for this upcoming project",
    selectWatchlistForCoin: "Select a watchlist for this coin",
    manageAllWatchlists: "Manage all watchlists",
    dataStoredInBrowser: "Data stored in browser",
    removeFromWatchlist: "Remove from watchlist",
    removeCoinFromWatchlist: "Remove {coinName} from watchlist",
    title: "Watchlists",
    description:
      "Track your favorite cryptocurrencies and receive real-time updates",
    portfolioScorePerformance: "Portfolio Score Performance",
    createNewWatchlist: "Create New Watchlist",
    coinWatchlistCreated: "Coin Watchlist Created",
    idoWatchlistCreated: "IDO Watchlist Created",
    coinWatchlistDeleted: "Coin Watchlist Deleted",
    idoWatchlistDeleted: "IDO Watchlist Deleted",
    coinWatchlistUpdated: "Coin Watchlist Updated",
    idoWatchlistUpdated: "IDO Watchlist Updated",
    hasBeenCreatedSuccessfully: "has been created successfully",
    hasBeenDeletedSuccessfully: "has been deleted successfully",
    hasBeenUpdatedSuccessfully: "has been updated successfully",
    failedToUpdateWatchlist: "Failed to update watchlist. Please try again.",
    watchlistNameCannotBeEmpty: "Watchlist name cannot be empty",
    watchlistCreated: "Watchlist Created",
    watchlistCreatedAndCoinAdded:
      "watchlist created and coin successfully added.",
    idoWatchlistCreatedAndProjectAdded:
      "IDO watchlist created and project successfully added.",
    noWatchlistSelectedToast: "No watchlist selected",
    rankTitle: "Rank",
    rankDescription: "Project ranking based on current sort order.",
    watchlistColumnTitle: "Watchlist",
    watchlistColumnDescription:
      "Add or remove projects from your personal watchlist.",
    removeFromIdoWatchlist: "Remove from IDO watchlist",
    upcomingFallback: "Upcoming",
    myWatchlist: "My Watchlist",
    createNewIdoWatchlist: "Create New IDO Watchlist",
    createNewWatchlistTitle: "Create New Watchlist",
    createIdoWatchlistDescription:
      "Create a new watchlist to track upcoming IDO projects",
    createWatchlistDescription:
      "Create a new watchlist to organize your favorite cryptocurrencies",
    createIdoWatchlistButton: "Create IDO Watchlist",
    createWatchlistButton: "Create Watchlist",
    defaultIdoDescription:
      "A collection of upcoming token launches I'm tracking",
    defaultCoinDescription: "A collection of my favorite cryptocurrencies",
    untitledWatchlist: "Untitled Watchlist",
    enterWatchlistNamePlaceholder: "Enter watchlist name",
    watchlistDescriptionPlaceholder: "Description of this watchlist",
    editWatchlist: "Edit Watchlist",
    deleteWatchlist: "Delete Watchlist",
    confirmDelete: "Are you sure you want to delete this watchlist?",
    confirmDeleteDescription:
      "This action cannot be undone. This will permanently delete your watchlist and all its contents.",
    cancel: "Cancel",
    delete: "Delete",
    shareDescription: "Share your watchlist with friends and community",
    copyLink: "Copy Link",
    linkCopied: "Link copied to clipboard!",
    watchlistName: "Watchlist Name",
    watchlistDescription: "Watchlist Description (Optional)",
    enterWatchlistName: "Enter watchlist name",
    enterDescription: "Enter description for your watchlist",
    createDescription:
      "Create a new watchlist to organize your favorite cryptocurrencies",
    editDescription: "Update your watchlist details",
    create: "Create",
    save: "Save Changes",
    addButton: "Add",
    added: "Added",
    default: "Default",
    notCompatible: "Not compatible",
    item: "item",
    items: "items",
    searchPlaceholder: "Search coins in watchlist...",
    noCoinsFound: "No coins found in this watchlist",
    addFirstCoin: "Add your first coin to get started",
    sortBy: "Sort by",
    viewMode: "View Mode",
    displayMode: "Display Mode",
    simple: "Simple",
    advanced: "Advanced",
    grid: "Grid",
    list: "List",
    untitled: "Untitled Watchlist",
    preview: "Preview",
    shareNote:
      "Note: By sharing this link, you make your watchlist publicly viewable",
    noWatchlistsAvailable: "No Watchlists Available",
    createFirstWatchlistUpcoming: "Create First Watchlist - Upcoming",
    upcomingProjectsDescription: "Upcoming IDO projects you're tracking",
    favoriteCollection: "A collection of my favorite cryptocurrencies",
  },

  // Empty state namespace
  empty: {
    title: "Your Watchlist is Empty",
    description:
      "Add coins you want to track and build your personalized watchlist",
    addCoins: "Add Coins",
    createWatchlist: "Create Watchlist",
    aiPoweredTitle: "AI-Powered & Data-Driven Crypto Ratings",
    trackDescription:
      "Track your favorite cryptocurrencies and receive real-time updates",
    coins: "Coins",
    idos: "IDOs",
  },

  notifications: {
    title: "Notifications",
    unread: "Unread",
    all: "All",
    alertTriggered: "{{coinName}} Alert Triggered",
    priceAbove: "{{coinName}} price above ${{threshold}}: ${{currentPrice}}",
    priceBelow: "{{coinName}} price below ${{threshold}}: ${{currentPrice}}",
    noNotifications: "No notifications found.",
    noUnreadNotifications: "No unread notifications",
    allCaughtUp: "When you receive notifications, they will appear here.",
  },

  coinDetail: {
    tokenSupplyDynamics: "Token Supply Dynamics",
    overallCategoriesMetricsBreakdown: "Overall Categories Metrics Breakdown",
    categories: "Categories",
    backToPreviousPage: "Back to Previous Page",
    rankPrefix: "Rank #",
    nextUnlock: "Next Unlock",
    unlockInDays: "Unlock in {days} days",
    about: "About",
    marketStatistics: "Market Statistics",
    allTimeHighLow: "All-Time High/Low",
    allTimeHigh: "All-Time High",
    allTimeLow: "All-Time Low",
    fromATH: "From ATH",
    fromATL: "From ATL",
    maxSupply: "Max Supply",
    circulatingSupply: "Circulating Supply",
    totalSupply: "Total Supply",
    ofMaxSupply: "of Maximum Supply",
    detailedInformation: "Detailed Information",
    showMore: "Show More",
    showLess: "Show Less",
  },

  priceChart: {
    title: "Price Chart",
    current: "Current",
    high: "High",
    low: "Low",
    sevenDays: "7D ",
    fourteenDays: "14D ",
    thirtyDays: "30D ",
  },

  scoreChart: {
    title: "Score Chart",
    current: "Current",
    high: "High",
    low: "Low",
    oneMonth: "1M ",
    threeMonths: "3M ",
    sixMonths: "6M ",
    oneyear: "1Y ",
    all: "All",
  },

  externalLinks: {
    title: "External Links",
  },

  format: {
    trillion: "T",
    billion: "B",
    million: "M",
    thousand: "K",
  },
  emissionScore: {
    undetermined: "Undetermined",
  },
  maxSupply: {
    uncapped: "Uncapped",
  },

  // Price Changes section
  priceChanges: {
    title: "Price Changes",
    "1d": "1 Day",
    "1day": "1 Day",
    "1w": "1 Week",
    "1week": "1 Week",
    "1m": "1 Month",
    "1month": "1 Month",
    "3m": "3 Months",
    "3months": "3 Months",
    "6m": "6 Months",
    "1y": "1 Year",
    "1year": "1 Year",
  },

  // Tokenomics namespace
  tokenomicsNamespace: {
    metricsBreakdown: "Tokenomics Metrics Breakdown",
    metricsBreakdownGeneric: "Metrics Breakdown",
    weight: "Weight",
    requestFeature: "Request Feature",
    reportError: "Report Error",
  },

  // Coin Health Score
  coinHealth: {
    title: "Coin Health Score",
    scoreRange: "Score Range",
    critical: "Critical",
    average: "Average",
    excellent: "Excellent",
    positive: "Positive",
    weak: "Weak",
  },

  // Score ratings namespace
  scoreRatings: {
    excellent: "Excellent",
    positive: "Positive",
    average: "Average",
    weak: "Weak",
    critical: "Critical",
  },

  // Methodology namespace
  methodology: {
    whatAreWeScoring: "What Are We Scoring?",
    whyIsThisImportant: "Why Is This Important?",
    scoringLevels: "Scoring Levels",
  },

  // Homepage namespace
  homepage: {
    // Hero section
    hero: {
      badge: "AI-Powered Crypto Analysis Platform",
      title: "Advanced AI-Driven Cryptocurrency Analysis",
      titleHighlight: "Platform",
      description:
        "Discover the future of cryptocurrency analysis with our comprehensive AI-powered platform. Get detailed insights, accurate ratings, and data-driven recommendations for smarter investment decisions.",
      ctaButton: "Start Analyzing",
      stats: {
        cryptocurrencies: "Cryptocurrencies",
        analysisMetrics: "Analysis Metrics",
        moreAccuracy: "More Accuracy",
      },
    },

    // Features sectionx
    features: {
      title: "Powerful Features for Every Crypto Investor",
      subtitle:
        "Comprehensive tools and AI-driven insights to help you make informed cryptocurrency investment decisions.",
      comprehensiveScoring: "Comprehensive AI scoring system",
      aiRating: {
        title: "AI-Enhanced Score Analysis",
        description:
          "Advanced machine learning algorithms analyze multiple data points to provide accurate cryptocurrency ratings and investment insights.",
        bullets: [
          "Real-time AI scoring across 40+ metrics",
          "Machine learning prediction models",
        ],
      },
      idoRating: {
        title: "IDO Rating System",
        description:
          "Comprehensive Initial DEX Offering analysis with AI-powered risk assessment and potential evaluation.",
        bullets: [
          "Pre-launch project evaluation",
          "Team and tokenomics analysis",
          "Launch potential scoring",
        ],
      },
      compareCoins: {
        title: "Advanced Coin Comparison",
        description:
          "Side-by-side comparison of cryptocurrencies with detailed metrics and AI-generated insights.",
        bullets: [
          "Multi-metric comparison dashboard",
          "AI-powered similarity analysis",
          "Risk-reward assessment",
        ],
      },
      portfolioGenerator: {
        title: "Smart Portfolio Generator",
        description:
          "AI-driven portfolio optimization based on your risk tolerance and investment goals.",
        bullets: [
          "Automated portfolio creation",
          "Risk-adjusted allocations",
          "Diversification optimization",
        ],
      },
      portfolioAnalysis: {
        title: "Portfolio Health Analysis",
        description:
          "Comprehensive analysis of your current portfolio with optimization recommendations.",
        bullets: [
          "Performance tracking",
          "Rebalancing suggestions",
          "Risk exposure analysis",
        ],
      },
      launchpads: {
        title: "Launchpad Integration",
        description:
          "Direct access to top cryptocurrency launchpads with curated project selections.",
        bullets: [
          "Verified launchpad projects",
          "Early access opportunities",
          "Due diligence reports",
        ],
      },
      aiAssistant: {
        title: "AI Trading Assistant",
        description:
          "Intelligent trading assistant powered by advanced AI to guide your investment decisions.",
        bullets: [
          "Real-time market insights",
          "Personalized recommendations",
          "Trading signal analysis",
        ],
      },
      airdropScore: {
        title: "Airdrop Score Analysis",
        description:
          "Evaluate airdrop opportunities with AI-powered scoring and eligibility assessment.",
        bullets: [
          "Airdrop potential scoring",
          "Eligibility requirements",
          "Historical success rates",
        ],
      },
      gemScout: {
        title: "Gem Scout Discovery",
        description:
          "Discover hidden cryptocurrency gems using advanced AI pattern recognition and market analysis.",
        bullets: [
          "Early-stage project discovery",
          "Market pattern analysis",
          "Growth potential scoring",
        ],
      },
    },

    // Badges
    badges: {
      betaTestingLive: "Beta Testing Live",
      live: "Live",
      betaTestingSoon: "Beta Testing Soon",
      comingSoon: "Soon",
    },

    // Trusted By section
    trustedBy: {
      title: "Backed By Industry Leaders",
      description:
        "Our platform integrates with leading cryptocurrency data providers and blockchain explorers to deliver the most accurate and comprehensive analysis.",
      stats: {
        coinsAnalyzed: "Coins Analyzed",
        dataPoints: "Data Points",
        apiCalls: "API Calls",
        dataSources: "Data Sources",
      },
      features: {
        aiPowered: "AI-Powered Analysis",
        multiLayer: "Multi-Layer Verification",
        enterprise: "Enterprise Grade",
      },
    },

    // Intelligence section
    intelligence: {
      title: "AI-Driven Intelligence",
      subtitle:
        "Experience the power of artificial intelligence in cryptocurrency analysis with real-time insights and predictive modeling.",
    },

    // Call to Action
    callToAction: {
      primary:
        "Experience the power of artificial intelligence in cryptocurrency analysis with real-time insights and predictive modeling.",
      secondary:
        "Join thousands of investors who trust our AI-powered analysis",
    },

    // Benefits
    benefits: {
      "0": {
        title: "Real-Time Analysis",
      },
      "1": {
        title: "AI-Powered Insights",
      },
      "2": {
        title: "Risk Assessment",
      },
    },

    // Buttons
    buttons: {
      getStarted: "Get Started",
      viewAllQuestions: "View All Questions",
    },

    // FAQ section
    faq: {
      title: "Frequently Asked Questions",
      questions: {
        "0": {
          question: "How accurate are the AI-powered cryptocurrency ratings?",
          answer:
            "Our AI models achieve over 85% accuracy in trend prediction by analyzing 40+ metrics including technical indicators, fundamental analysis, social sentiment, and market data from multiple sources.",
        },
        "1": {
          question:
            "What makes CoinScout different from other crypto analysis platforms?",
          answer:
            "CoinScout combines advanced AI algorithms with real-time data from multiple sources, providing comprehensive analysis that goes beyond basic price tracking to include tokenomics, team evaluation, and market sentiment analysis.",
        },
        "2": {
          question:
            "Is the platform suitable for both beginners and experienced traders?",
          answer:
            "Yes, our platform is designed with multiple complexity levels. Beginners can use simplified views and AI recommendations, while experienced traders can access detailed metrics, custom analysis tools, and advanced comparison features.",
        },
      },
    },

    // Testimonials
    testimonials: {
      title: "What Our Users Say",
      "0": {
        text: "CoinScout's AI analysis helped me identify profitable opportunities I would have missed otherwise. The comprehensive scoring system is incredibly accurate.",
      },
    },
  },

  // Error namespace
  error: {
    somethingWentWrong: "Something went wrong",
    refreshPage: "Refresh Page",
    goToHome: "Go to Home",
    clearErrorLogs: "Clear Error Logs",
  },

  // Footer colon-separated keys for compatibility
  "footer:description":
    "Advanced AI-powered cryptocurrency analysis platform providing comprehensive insights, ratings, and data-driven recommendations for smarter investment decisions.",
  "footer:allRightsReserved": "All rights reserved.",
  "footer:categories.product": "Product",
  "footer:categories.learn": "Learn",
  "footer:categories.community": "Community",
  "footer:categories.legal": "Legal",
  "footer:links.cryptoRatings": "Crypto Ratings",
  "footer:links.idoRatings": "IDO Ratings",
  "footer:links.aiPortfolioStrategist": "AI Portfolio Strategist",
  "footer:links.aiPortfolioCheckup": "AI Portfolio Checkup",
  "footer:links.compareCoins": "Compare Coins",
  "footer:links.recentListings": "Recent Listings",
  "footer:links.topMovers": "Top Movers",
  "footer:links.airdropsHub": "Airdrops Hub",
  "footer:links.scoutAI": "Scout AI",
  "footer:links.aiAssistant": "AI Assistant",
  "footer:links.pricing": "Pricing",
  "footer:links.academy": "Academy",
  "footer:links.documentation": "Documentation",
  "footer:links.roadmap": "Roadmap",
  "footer:links.blog": "Blog",
  "footer:links.faq": "FAQ",
  "footer:links.forum": "Forum",
  "footer:links.telegram": "Telegram",
  "footer:links.discord": "Discord",
  "footer:links.twitter": "Twitter",
  "footer:links.publicPortfolios": "Public Portfolios",
  "footer:links.communityGuidelines": "Community Guidelines",
  "footer:links.userTestimonials": "User Testimonials",
  "footer:links.privacyPolicy": "Privacy Policy",
  "footer:links.termsOfService": "Terms of Service",
  "footer:links.cookiePolicy": "Cookie Policy",
  "footer:links.disclaimer": "Disclaimer",
  "footer:links.advertisingPolicy": "Advertising Policy",
  "footer:links.careers": "Careers",
  "footer:links.soon": "Soon",

  // Roadmap translations
  roadmap: {
    title: "Development Roadmap",
    subtitle: "Track our progress, share feedback, and see what's coming next to CoinScout",
    loading: "Loading Roadmap",
    loadingSubtitle: "Fetching the latest development updates...",
    error: "Error Loading Roadmap",
    errorSubtitle: "Failed to load roadmap data",
    tryAgain: "Try Again",
    showMore: "Show More",
    showLess: "Show Less",
    completed: "Completed",
    categories: {
      all: "All Categories",
      feature: "Feature", 
      enhancement: "Enhancement",
      "bug-fix": "Bug Fix",
      integration: "Integration"
    },
    status: {
      completed: "Completed",
      "in-progress": "In Progress", 
      planned: "Planned"
    },
    priority: {
      high: "High",
      medium: "Medium",
      low: "Low"
    },
    columns: {
      featuresTitle: "Features & Enhancements",
      featuresDescription: "Planned features and improvements",
      bugsTitle: "Bug Fixes", 
      bugsDescription: "Resolved issues and bug fixes",
      progressTitle: "Work in Progress",
      progressDescription: "Currently under development"
    },
    stats: {
      totalItems: "Total Items",
      completed: "Completed",
      inProgress: "In Progress", 
      planned: "Planned",
      communityVotes: "Community Votes"
    }
  },

  // Upcoming IDO translations
  upcoming: {
    title: "Upcoming Token Sales",
    subtitle: "Discover and evaluate new tokens before launch",
    search: "Search upcoming token sales...",
    noResults: "No upcoming token sales found",
    loading: "Loading upcoming token sales...",
    error: "Error loading upcoming token sales",
    retryButton: "Retry",
    tba: "TBA",
    rank: "Rank #{number}",
    saleType: "Sale Type",
    points: "points",
    tokenomics: "Tokenomics",
    security: "Security",
    social: "Social",
    market: "Market",
    insights: "Insights",
    totalAiScore: "Total AI Score",
    filters: {
      title: "Filters",
      description: "Filter upcoming token sales by various criteria",
      projectScore: "Project Score",
      saleType: "Sale Type",
      category: "Category",
      blockchain: "Blockchain",
      allTypes: "All Types",
      allCategories: "All Categories",
      allBlockchains: "All Blockchains",
      searchCategories: "Search categories...",
      searchChains: "Search blockchains...",
      selectDateRange: "Select date range",
      last24Hours: "Last 24 Hours",
      last7Days: "Last 7 Days",
      last14Days: "Last 14 Days",
      last30Days: "Last 30 Days",
      last90Days: "Last 90 Days",
      reset: "Reset Filters",
      apply: "Apply Filters",
    },
    table: {
      name: "Name",
      date: "IDO Date",
      launchDate: "IDO Date",
      initialCap: "Initial Cap",
      totalRaised: "Total Raised",
      score: "Score",
      actions: "Actions",
      imcScore: "Initial Cap",
      fundingScore: "Funding",
      launchpadScore: "Launchpad",
      investorScore: "Investors",
      socialScore: "Social",
      totalAiScore: "Total AI Score",
    },
    tooltips: {
      rank: {
        title: "Rank",
        description: "Project ranking based on current sort order.",
      },
      watchlist: {
        title: "Watchlist",
        description: "Add or remove projects from your personal watchlist.",
      },
      projectName: {
        title: "Project Name",
        description:
          "Official name of the cryptocurrency project and its ticker symbol.",
      },
      launchDate: {
        title: "IDO Date",
        description:
          "Scheduled or confirmed date for the Initial DEX Offering (IDO) and token launch.",
      },
      initialMarketCap: {
        title: "Initial Market Cap",
        description:
          "Initial Market Cap metric evaluates the projected market capitalization at token launch, considering factors like token supply, initial price, and market conditions.",
      },
      funding: {
        title: "Funding Analysis",
        description:
          "Funding Score metric analyzes the project's funding history, investor quality, and financial sustainability.",
      },
      launchpad: {
        title: "Launchpad Analysis",
        description:
          "Launchpad Score metric evaluates the reputation and track record of the platform conducting the token sale.",
      },
      investors: {
        title: "Investor Analysis",
        description:
          "Investor Score metric assesses the quality and reputation of backing investors and venture capital firms.",
      },
      socialMedia: {
        title: "Social Media Analysis",
        description:
          "Social Score metric quantifies community interest by analyzing follower counts, engagement rates, and growth trends across a project's social media presence.",
      },
      coinScoutAiScore: {
        title: "CoinScout AI Score",
        description: "The overall rating calculated from all scoring metrics.",
      },
      projectDetails: {
        title: "Project Details",
        description:
          "View comprehensive analysis and detailed metrics for this project.",
      },
    },
  },

  // Sale type translations
  saleType: {
    IDO: "IDO",
    IEO: "IEO",
    ICO: "ICO",
    SHO: "SHO",
    Seed: "Seed",
    IGO: "IGO",
    ISO: "ISO",
  },

  // Logs translations (for development)
  logs: {
    usingIdoWatchlists: "Using IDO watchlists:",
    fetchInitialDataStarted: "Fetch initial data started",
    rawAPIData: "Raw API data:",
    apiDataLength: "API data length:",
    fetchInitialDataCompleted: "Fetch initial data completed",
    firstRecordDetails: "First record details:",
    imageUrlChecks: "Image URL checks",
    allProjectsFromAPI: "All projects from API",
    usingDirectAPI: "Using direct API",
    rawAPIDataFirst5: "Raw API data first 5:",
    firstRecordSocialScore: "First record social score:",
    clickedProjectInfo: "Clicked project info:",
    redirectingWithDirectID: "Redirecting with direct ID:",
  },

  // Empty state translations
  emptyState: {
    noData: "No data available",
    noDescription: "No description available",
    noTeamInfo: "No team information available",
    noFundingInfo: "No funding information available",
    noTokenomics: "No tokenomics data available",
    noPriceAnalysis: "No price analysis available",
    noProjectDetails: "No project details available",
    noInvestorInfo: "No investor information available",
  },

  // IDO feature request translations
  ido: {
    // Status translations
    status: {
      excellent: "Excellent",
      good: "Good",
      fair: "Fair",
      poor: "Poor",
      bad: "Bad",
      upcoming: "Upcoming",
      new: "New",
      active: "Active",
      growing: "Growing",
      mature: "Mature",
      declining: "Declining",
    },
    // Common UI elements
    error: "Error",
    loadingData: "Loading IDO data...",
    noDataAvailable: "No data available",
    fetchingData: "Fetching data from API...",
    unexpectedError: "An unexpected error occurred",
    backToList: "Back to List",
    featureRequest: {
      title: "Request a Feature",
      description: "Tell us what feature you'd like to see in CoinScout.",
      enterDescription: "Please enter your feature suggestion",
      submitted: "Feature Request Submitted!",
      thankYou:
        "Thank you for your feedback! Your suggestion will be reviewed by our team.",
    },
    // Social media and links (moved to end of ido namespace to avoid duplicates)
    medium: "Medium",
    linkedin: "LinkedIn",
    reddit: "Reddit",
    github: "GitHub",
    whitepaper: "Whitepaper",
    explorer: "Blockchain Explorer",
    uncategorized: "Uncategorized",
    submitError: "Submission Error",
    submitErrorDescription:
      "An error occurred while submitting your suggestion. Please try again later.",
    errorReport: {
      enterDetails: "Please enter error details",
      submitted: "Error Report Submitted!",
      thankYou:
        "Thank you for reporting the error. Our team will review it as soon as possible.",
    },
    // Contract information translations
    contract: "Contract",
    contractAddress: "Contract Address",
    tokenType: "Token Type",
    decimals: "Decimals",
    copied: "Copied!",
    contractAddressCopied: "Contract address copied to clipboard",
    metrics: {
      idoPrice: "IDO Price",
      imc: "IMC",
      fdv: "FDV",
      fundsRaised: "Funds Raised",
      initialMarketCap: "Initial Market Cap",
      marketContext: "Market Context",
      analysis: "IDO Metrics Analysis",
      maxSupply: "Max Supply",
      totalSupply: "Total Supply",
      initialCirculating: "Initial Circulating",
      basedOn: "Based on {{count}} metrics",
      breakdown: "IDO Metrics Breakdown",
    },
    advancedAnalysis: "Advanced Analysis",
    tabs: {
      team: "Team",
      funding: "Funding & Investment",
      tokenomics: "Tokenomics",
      releases: "Token Release",
      analysis: "Price Analysis",
      details: "Project Details",
    },
    funding: {
      insights: "Funding Insights",
      totalRaised: "Total Raised",
      rounds: "Funding Rounds",
      athRoi: "ATH ROI",
      currentRoi: "Current ROI",
      saleStartDate: "Sale Start Date",
      saleEndDate: "Sale End Date",
      valuationGrowth: "Valuation Growth",
      seedValuation: "Seed Valuation",
      privateSale: "Private Sale",
      publicSale: "Public Sale",
    },
    investors: {
      keyInvestors: "Key Investors",
      investor: "Investor",
      type: "Type",
      amount: "Amount",
    },
    projectDescription: "Project Description",
    whatIs: "What is",
    website: "Website",
    tokenAllocation: {
      title: "Token Allocation Matrix",
      description: "Advanced distribution analytics and tokenomics breakdown",
      analytics: "Distribution Analytics",
    },
    twitter: "Twitter / X",
    telegram: "Telegram",
    discord: "Discord",
    contracts: "Contracts",
    links: "Links",
    copyContractAddress: "Copy contract address",
  },

  // Profile page translations
  profile: {
    title: "Profile",
    description: "Manage your account settings and preferences.",
    saveChanges: "Save Changes",
    profile: "Profile",
    notifications: "Notifications",
    security: "Security",
    settings: "Settings",
    achievements: "Achievements",
    membership: "Membership",

    // Profile tab
    username: "Username",
    email: "Email",
    emailPlaceholder: "<EMAIL>",
    bio: "Bio",
    bioPlaceholder: "Tell us about yourself...",
    avatar: "Avatar",
    membershipTier: "Membership Tier",
    premium: "Premium",
    pro: "Pro",
    free: "Free",
    choosePlan: "Choose Plan",
    status: "Status",
    active: "Active",
    lastLogin: "Last login",
    never: "Never",
    verified: "Verified",
    resendVerification: "Resend verification email",
    areYouSure: "Are you sure?",

    // Subscription details
    plan: "Plan",

    contactSupport: "Contact Support",
    planStatus: "Plan Status",
    started: "Started",
    expires: "Expires",
    memberSince: "Member since",
    // Account management
    manageSubscription: "Manage your subscription in the",
    tab: "tab",
    downloadData: "Download Your Data",
    deleteAccount: "Delete Account",
    absolutelySure: "Are you absolutely sure?",
    deleteWarning:
      "This action cannot be undone. This will permanently delete your account and remove your data from our servers.",
  },

  // Subscription Modal
  subscriptionModal: {
    title: "Subscription Upgrade Required",
    defaultMessage:
      "You need to upgrade your subscription plan to use this feature.",
    currentPlan: "Current Plan",
    viewPricingPlans: "View Pricing Plans",
    upgradeNote: "You can change or cancel your plan at any time.",
  },

  // IDO Table translations
  idoTable: {
    name: {
      title: "Name",
      description: "The name of the project and its token symbol.",
    },
    date: {
      title: "IDO Date",
      description:
        "The date when the Initial DEX Offering (IDO) will take place.",
    },
    imcScore: {
      title: "IMC Score",
      description:
        "Initial Market Cap score that evaluates the project's token valuation at launch.",
    },
    fundingScore: {
      title: "Funding Score",
      description:
        "Funding Score evaluates the project's fundraising history, investor quality, and financial backing.",
    },
    launchpadScore: {
      title: "Launchpad Score",
      description:
        "Launchpad Score assesses the credibility and track record of the platform conducting the token sale.",
    },
    investorScore: {
      title: "Investor Score",
      description:
        "Investor Score is a metric that evaluates the credibility and track record of the project's investors and backing entities.",
    },
    socialScore: {
      title: "Social Score",
      description:
        "Social Score evaluates the project's community engagement, social media presence, and overall brand awareness.",
    },
    aiScore: {
      title: "AI Score",
      description:
        "Comprehensive AI-driven evaluation that combines all individual metrics into a weighted overall score.",
    },
    details: {
      title: "Details",
      description:
        "View comprehensive analysis of the project including all metrics, historical data, and in-depth reports.",
    },
  },

  // Pricing page translations
  pricing: {
    title: "Simple pricing for every investor",
    description:
      "Choose the plan that best fits your investment strategy. All plans include access to our core analysis features.",
    monthly: "Monthly",
    yearly: "Yearly",
    mostPopular: "Most Popular",
    selectPlan: "Select Plan",
    getStarted: "Get Started",
    savingsText: "Save with annual billing",
    priceDifference: "Price Difference",
    currentPlan: "Your Current Plan",
    whatsIncluded: "What's included:",
    save: "Save 17%",
    choosePlan: "Choose Plan",
    haveQuestions: "Still have questions?",
    planNames: {
      free: "Free",
      basic: "Basic",
      advance: "Advance",
      premium: "Premium",
    },
    descriptions: {
      free: "Basic access with limited features",
      basic: "Get started with crypto investing",
      advance: "Advanced tools for active traders",
      premium: "Enterprise access with no limits",
    },
    subtitles: {
      free: "Perfect for beginners",
      basic: "For traders who need more",
      advance: "For established investors",
      premium: "For professional traders",
    },
    haveQuestion: "Still have questions?",
  },

  // Support translations
  support: {
    contactSupport: "Contact Support",
    contactSupportTitle: "Contact Support",
    contactSupportDescription:
      "Please fill out the form below and our support team will get back to you as soon as possible.",
  },

  // Common translations with colon-separated keys for compatibility
  "common:logs.firstRecordSocialScore": "First record social score:",
  "common:logs.clickedProjectInfo": "Clicked project info:",
  "common:logs.redirectingWithDirectID": "Redirecting with direct ID:",
  "common:emptyState.noData": "No data available",
  "common:emptyState.noInvestorInfo": "No investor information available",
  "common:pricing.save": "Save",
  "common:subscriptionModal.title": "Subscription Required",
  "common:subscriptionModal.defaultMessage":
    "To access this feature, you need to upgrade your subscription.",
  "common:subscriptionModal.currentPlan": "Current Plan",
  "common:subscriptionModal.viewPricingPlans": "View Pricing Plans",
  "common:subscriptionModal.upgradeNote":
    "Upgrade your subscription to unlock all features",
};
// Added missing keys  "common:auth:validation.password.lowercase": "En az bir küçük harf içermeli",  "common:auth:validation.password.min": "Şifre en az 8 karakter olmalı",  "common:auth:validation.password.special": "En az bir özel karakter içermeli",  "common:auth:validation.password.uppercase": "En az bir büyük harf içermeli",
