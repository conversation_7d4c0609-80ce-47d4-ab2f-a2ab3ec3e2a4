import React, { useMemo, useState } from 'react';
import { motion } from "framer-motion";
import { cn } from "@/lib/utils";
import { Link, useLocation } from "wouter";
import { useSidebarv2 } from "@/contexts/SidebarContextv2";
import { useLanguage } from "@/contexts/LanguageContext";

import {
  BarChart2,
  ArrowLeftRight,
  Bookmark,
  Briefcase,
  Calendar,
  TrendingUp,
  Sparkles,
  Rocket,
  SmilePlus,
  Bot,
  Diamond,

  ChevronRight,
  ChevronLeft,
  ChevronDown,
  Wand2,
  Gift,
  BookOpen,
  Users,
  MessageSquare,
  BookText,
  LineChart,
  Lock,
  Play,
  FileText,
  Wrench,
  Gamepad,
  ShieldAlert,
  Network,
  Shield,
  Layers,
  Vote,
  Twitter,
  Send,
  Instagram,
  GraduationCap,

  Home,
  Map as MapIcon,
  Microscope,
  ExternalLink
} from "lucide-react";
import { Badge } from "@/components/ui/badge";
import { SiDiscord, SiTelegram, SiYoutube, SiMedium, SiTiktok } from "react-icons/si";

const STYLES = {
  sidebar: "fixed left-0 top-16 h-[calc(100vh-4rem)] bg-card/98 backdrop-blur-md border-r border-border/60 flex flex-col z-50 shadow-lg shadow-black/5",
  toggleButton: "absolute -right-5 top-12 w-10 h-10 bg-primary/15 hover:bg-primary/25 rounded-full flex items-center justify-center text-primary transition-colors duration-200 border border-border/60 shadow-md focus:ring-2 focus:ring-primary/30 focus:outline-none",
  menuContainer: "flex flex-col space-y-1 p-3 mt-2 overflow-y-auto",
  menuItem: "flex items-center px-3 py-3 rounded-lg cursor-pointer group hover:bg-primary/8 transition-colors duration-200 min-h-[44px] [.sidebar-collapsed_&]:justify-center [.sidebar-collapsed_&]:px-2 focus:outline-none focus:ring-2 focus:ring-primary/50",
  menuSubItem: "flex items-center px-3 py-2.5 rounded-lg cursor-pointer group hover:bg-primary/6 transition-colors duration-200 ml-4 min-h-[40px] border-l-2 border-transparent hover:border-primary/20 [.sidebar-collapsed_&]:justify-center [.sidebar-collapsed_&]:ml-0 [.sidebar-collapsed_&]:px-2",
  icon: "w-5 h-5 text-muted-foreground/80 group-hover:text-primary transition-colors duration-200 [.sidebar-collapsed_&]:mx-auto flex-shrink-0",
  label: "ml-3 text-[15px] leading-6 font-medium text-muted-foreground/90 group-hover:text-foreground whitespace-nowrap [.sidebar-collapsed_&]:hidden",
  separator: "my-3 border-t border-border/40 mx-3",
  arrowIcon: "w-4 h-4 text-muted-foreground/60 group-hover:text-primary transition-colors duration-200 ml-auto [.sidebar-collapsed_&]:hidden flex-shrink-0",

  activeDot: "w-2 h-2 rounded-full bg-primary ml-auto flex-shrink-0"
} as const;

// Enhanced responsive widths following industry standards
const sidebarVariants = {
  expanded: { width: "280px" },  // Industry standard: 240-280px
  collapsed: { width: "64px" }   // Better touch target accommodation
} as const;

const labelVariants = {
  visible: { opacity: 1, x: 0 },
  hidden: { opacity: 0, x: -10 }
} as const;

const useSidebarItems = () => {
  const { t } = useLanguage();
  
  return useMemo(() => [
    // Core Features
    { icon: BarChart2, text: t('sidebar.coin_ratings'), path: "/coinlistv2", category: "main" },
    { icon: Calendar, text: t('sidebar.ido_ratings'), path: "/upcomingv2", category: "main" },
    
    { type: 'separator' },
    
    // Portfolio
    {
      icon: Briefcase,
      text: t('sidebar.portfolio'),
      children: [
        { icon: Bookmark, text: t('sidebar.my_watchlist'), path: "/watchlistv2" },
        { icon: Briefcase, text: t('sidebar.ai_portfolio_audit'), path: "/portfolio-analysis" },
        { icon: Wand2, text: t('sidebar.ai_portfolio_generator'), path: "/generator" }
      ]
    },
    
    // Alpha Tools
    {
      icon: TrendingUp,
      text: t('sidebar.alpha_tools'),
      children: [
        { icon: Layers, text: t('sidebar.compare_coins'), path: "/compare" },
        { icon: Rocket, text: t('sidebar.top_launchpads'), path: "/launchpads" },
        { icon: Gift, text: t('sidebar.airdrops_hub'), path: "/airdropscore" }
      ]
    },
    
    // AI Tools
    {
      icon: Bot,
      text: t('sidebar.ai_tools'),
      children: [
        { icon: Bot, text: t('sidebar.ai_assistant'), path: "/aiassistant" },
        { icon: Microscope, text: t('sidebar.ai_research'), path: "/research", soon: true }
      ]
    },
    
    { type: 'separator' },
    
    // CoinScout Academy
    { icon: GraduationCap, text: t('sidebar.coinscout_academy'), path: "#", soon: true, disabled: true }
  ], [t]);
};

interface Sidebarv2Props {
  className?: string;
}

export function Sidebarv2({ className }: Sidebarv2Props) {
  const { isExpanded, isPermanentlyExpanded, setIsExpanded, setIsPermanentlyExpanded } = useSidebarv2();
  const [isHovered, setIsHovered] = useState(false);
  const [expandedSection, setExpandedSection] = useState<string | null>(null);
  const sidebarItems = useSidebarItems();
  const [location] = useLocation();
  const { t } = useLanguage();
  
  // Don't render the sidebar on certain pages (documentation, pricing, home/landing page, faq, privacy, terms, cookie-policy, disclaimer)
  // Also don't render the sidebar on admin routes
  const shouldHideSidebar = useMemo(() => {
    const hiddenPages = ["/docusaurus-docs", "/pricing", "/", "/privacy", "/terms", "/cookie-policy", "/disclaimer"];
    return hiddenPages.includes(location) || location.startsWith("/admin");
  }, [location]);



  // Check if current page is active
  const isActive = (path: string) => location === path;
  const hasActiveChild = (children: any[]) => 
    children?.some(child => isActive(child.path));

  const toggleSection = (text: string) => {
    setExpandedSection(expandedSection === text ? null : text);
  };
  
  const handleMouseEnter = () => {
    setIsHovered(true);
    if (!isPermanentlyExpanded) {
      setIsExpanded(true);
    }
  };
  
  const handleMouseLeave = () => {
    setIsHovered(false);
    if (!isPermanentlyExpanded) {
      // Remove the timeout delay but keep the smooth animation
      // controlled by the framer-motion transition
      setIsExpanded(false);
    }
  };
  
  const handleToggleButtonClick = () => {
    // When the sidebar is expanded (either by hover or permanently)
    if (isExpanded) {
      // If it's permanently expanded, toggle back to hover mode
      if (isPermanentlyExpanded) {
        setIsPermanentlyExpanded(false);
        
        // If mouse isn't hovering, collapse immediately
        if (!isHovered) {
          setIsExpanded(false);
        }
      } 
      // If it's expanded by hover, make it permanently expanded
      else {
        setIsPermanentlyExpanded(true);
      }
    }
    // This case should not happen with hover functionality,
    // but keeping it for completeness
    else {
      setIsExpanded(true);
      setIsPermanentlyExpanded(true);
    }
  };
  
  // No cleanup needed as we removed the timeout

  // Debug logging to see what's happening
  console.log("Sidebarv2 Debug - Location:", location);
  console.log("Sidebarv2 Debug - shouldHideSidebar:", shouldHideSidebar);
  
  if (shouldHideSidebar) {
    console.log("Sidebarv2 Debug - Returning null (hiding sidebar)");
    return null;
  }

  return (
    <motion.nav
      initial="collapsed"
      animate={isExpanded ? "expanded" : "collapsed"}
      variants={sidebarVariants}
      transition={{ duration: 0.3, ease: "easeInOut" }}
      className={cn(STYLES.sidebar, !isExpanded && "sidebar-collapsed", className)}
      onMouseEnter={handleMouseEnter}
      onMouseLeave={handleMouseLeave}
      role="navigation"
      aria-label="Main navigation"
      aria-expanded={isExpanded}
    >
      {/* Top toggle button with descriptive text */}
      <div className="mb-2 px-0.5">
        <button
          onClick={handleToggleButtonClick}
          className={`flex items-center gap-1 px-1 py-1.5 w-full text-sm transition-colors rounded-md focus:outline-none focus:ring-2 focus:ring-primary/50
            ${!isExpanded ? 'justify-center' : ''}
            ${isPermanentlyExpanded ? 'bg-primary/20 hover:bg-primary/30' : 'hover:bg-muted/50'}`}
          aria-label={isPermanentlyExpanded ? "Unlock sidebar" : "Lock sidebar"}
          aria-pressed={isPermanentlyExpanded}
        >
          <ChevronLeft className={`w-4 h-4 transition-transform ${isPermanentlyExpanded ? 'text-primary rotate-180' : ''}`} />
          {isExpanded && (
            <span className={`text-xs font-medium truncate ${isPermanentlyExpanded ? 'text-primary/90' : ''}`}>
              {isPermanentlyExpanded ? t('sidebar.unlock') : t('sidebar.lock')}
            </span>
          )}
        </button>
      </div>



        <div className={STYLES.menuContainer}>
          {sidebarItems.map((item: any, index) => (
            <div key={index}>
              {item.type === 'separator' ? (
                <div className={STYLES.separator} />
              ) : item.children ? (
                <div>
                  <motion.button
                    className={cn(
                      STYLES.menuItem,
                      hasActiveChild(item.children) && "bg-primary/12 text-primary relative",
                      "w-full text-left focus:outline-none focus:ring-2 focus:ring-primary/50 relative overflow-hidden"
                    )}
                    whileHover={{ x: 5 }}
                    onClick={() => isExpanded && toggleSection(item.text)}
                    aria-expanded={expandedSection === item.text}
                    aria-controls={`submenu-${index}`}
                    tabIndex={0}
                  >
                    <item.icon className={cn(
                      STYLES.icon,
                      hasActiveChild(item.children) && "text-primary"
                    )} />
                    {isExpanded && (
                      <>
                        <motion.span
                          variants={labelVariants}
                          initial="hidden"
                          animate="visible"
                          transition={{ delay: 0.1 }}
                          className={cn(
                            STYLES.label,
                            hasActiveChild(item.children) && "text-primary font-medium"
                          )}
                        >
                          {item.text}
                        </motion.span>
                        {item.soon && (
                          <Badge 
                            variant="secondary" 
                            className="ml-2 text-[10px] px-1.5 py-0.5 bg-amber-500/20 text-amber-400 border-amber-500/30"
                          >
                            {t('sidebar.soon')}
                          </Badge>
                        )}
                        {expandedSection === item.text ? (
                          <ChevronDown className={STYLES.arrowIcon} />
                        ) : (
                          <ChevronRight className={STYLES.arrowIcon} />
                        )}
                      </>
                    )}
                  </motion.button>
                  
                  {isExpanded && expandedSection === item.text && (
                    <div id={`submenu-${index}`} role="menu" className="ml-2">
                      {item.children.map((child: any, childIndex: number) => (
                        child.external ? (
                          <a key={`${index}-${childIndex}`} href={child.path} target="_blank" rel="noopener noreferrer">
                            <motion.div
                              className={cn(
                                STYLES.menuSubItem,
                                isActive(child.path) && "bg-primary/20 text-primary"
                              )}
                              whileHover={{ x: 5 }}
                              role="menuitem"
                              tabIndex={0}
                            >
                              <child.icon className={cn(
                                STYLES.icon,
                                isActive(child.path) && "text-primary"
                              )} />
                              {isExpanded && (
                                <>
                                  <motion.span
                                    variants={labelVariants}
                                    initial="hidden"
                                    animate="visible"
                                    transition={{ delay: 0.1 }}
                                    className={cn(
                                      STYLES.label,
                                      isActive(child.path) && "text-primary font-medium"
                                    )}
                                  >
                                    {child.text}
                                  </motion.span>
                                  {child.soon && (
                                    <Badge 
                                      variant="secondary" 
                                      className="ml-2 text-[10px] px-1.5 py-0.5 bg-amber-500/20 text-amber-400 border-amber-500/30"
                                    >
                                      {t('sidebar.soon')}
                                    </Badge>
                                  )}
                                  <ExternalLink className="w-3 h-3 text-muted-foreground/60 ml-auto" />
                                </>
                              )}
                            </motion.div>
                          </a>
                        ) : (
                          <Link key={`${index}-${childIndex}`} href={child.path}>
                            <motion.div
                              className={cn(
                                STYLES.menuSubItem,
                                isActive(child.path) && "bg-primary/20 text-primary"
                              )}
                              whileHover={{ x: 5 }}
                              role="menuitem"
                              tabIndex={0}
                            >
                              <child.icon className={cn(
                                STYLES.icon,
                                isActive(child.path) && "text-primary"
                              )} />
                              {isExpanded && (
                                <>
                                  <motion.span
                                    variants={labelVariants}
                                    initial="hidden"
                                    animate="visible"
                                    transition={{ delay: 0.1 }}
                                    className={cn(
                                      STYLES.label,
                                      isActive(child.path) && "text-primary font-medium"
                                    )}
                                  >
                                    {child.text}
                                  </motion.span>
                                  {child.soon && (
                                    <Badge 
                                      variant="secondary" 
                                      className="ml-2 text-[10px] px-1.5 py-0.5 bg-amber-500/20 text-amber-400 border-amber-500/30"
                                    >
                                      {t('sidebar.soon')}
                                    </Badge>
                                  )}
                                </>
                              )}
                              {isActive(child.path) && (
                                <div className="ml-auto w-1.5 h-1.5 rounded-full bg-primary" />
                              )}
                            </motion.div>
                          </Link>
                        )
                      ))}
                    </div>
                  )}
                </div>
              ) : item.disabled ? (
                <motion.div
                  className={cn(
                    STYLES.menuItem,
                    "opacity-50 cursor-not-allowed",
                    "focus:outline-none focus:ring-2 focus:ring-primary/50"
                  )}
                  tabIndex={0}
                  role="menuitem"
                >
                  <item.icon className={cn(
                    STYLES.icon,
                    "text-muted-foreground/50"
                  )} />
                  {isExpanded && (
                    <>
                      <motion.span
                        variants={labelVariants}
                        initial="hidden"
                        animate="visible"
                        transition={{ delay: 0.1 }}
                        className={cn(
                          STYLES.label,
                          "text-muted-foreground/50"
                        )}
                      >
                        {item.text}
                      </motion.span>
                      {item.soon && (
                        <Badge 
                          variant="secondary" 
                          className="ml-2 text-[10px] px-1.5 py-0.5 bg-amber-500/20 text-amber-400 border-amber-500/30"
                        >
                          {t('sidebar.soon')}
                        </Badge>
                      )}
                    </>
                  )}
                </motion.div>
              ) : (
                <Link href={item.path}>
                  <motion.div
                    className={cn(
                      STYLES.menuItem,
                      isActive(item.path) && "bg-primary/20 text-primary",
                      "focus:outline-none focus:ring-2 focus:ring-primary/50"
                    )}
                    whileHover={{ x: 5 }}
                    tabIndex={0}
                    role="menuitem"
                  >
                    <item.icon className={cn(
                      STYLES.icon,
                      isActive(item.path) && "text-primary"
                    )} />
                    {isExpanded && (
                      <>
                        <motion.span
                          variants={labelVariants}
                          initial="hidden"
                          animate="visible"
                          transition={{ delay: 0.1 }}
                          className={cn(
                            STYLES.label,
                            isActive(item.path) && "text-primary font-medium"
                          )}
                        >
                          {item.text}
                        </motion.span>
                        {item.soon && (
                          <Badge 
                            variant="secondary" 
                            className="ml-2 text-[10px] px-1.5 py-0.5 bg-amber-500/20 text-amber-400 border-amber-500/30"
                          >
                            {t('sidebar.soon')}
                          </Badge>
                        )}
                      </>
                    )}
                    {isActive(item.path) && (
                      <div className="ml-auto w-1.5 h-1.5 rounded-full bg-primary" />
                    )}
                  </motion.div>
                </Link>
              )}
            </div>
          ))}
        </div>
      </motion.nav>
    );
}