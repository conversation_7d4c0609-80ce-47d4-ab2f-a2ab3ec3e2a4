# CoinScout Platform

## Overview
CoinScout is an AI-powered cryptocurrency analysis platform providing data-driven fundamental ratings. It aims to deliver real-time market data, analysis, and scoring through a modern React frontend connected to external cryptocurrency APIs. The platform's vision is to be a comprehensive tool for cryptocurrency analysis, leveraging AI for fundamental ratings and offering a rich user experience with real-time data and actionable insights.

## User Preferences
Preferred communication style: Simple, everyday language.

## Recent Changes (January 2025)
- **Auth Redirects Updated to v2 (January 21, 2025)** - All authentication redirects now point to /coinlistv2 instead of /coinlist
  - Login success redirect updated in use-auth.tsx to redirect to /coinlistv2
  - Registration email verification dialog redirect updated to /coinlistv2
  - Added security validation for returnTo parameter to prevent open redirect attacks
  - Fixed duplicate navigation issue in registration flow
- **DEFINITIVE RESOLUTION: "Element type is invalid" errors SOLVED** - Completely resolved all React import issues through systematic @/ alias to relative import conversion (January 20, 2025)
  - App.tsx: Fixed all lazy-loaded route imports and core component imports (25+ imports)
  - CoinListv2.tsx: Converted all 29 @/ imports to relative paths
  - PortfolioAnalysisOptimized.tsx: Fixed all component and utility imports  
  - EnhancedScoreIndicator.tsx: Fixed critical @/ imports causing component null returns
  - coin-detail/ScoreGauge.tsx: Resolved @/ lib imports
  - All score components, UI components, and utility imports now use consistent relative paths
- **Export/Import compatibility verified** - All named/default export mismatches resolved, TypeScript compatibility fully restored
- **/coinlistv2 route fully operational** - Route loads successfully without any "Element type is invalid" errors
- **Portfolio Analysis component integration maintained** - 9-column table with Asset, Holdings, Allocation, Value, Profit/Loss, Price, 24h %, 🤖 Total AI Score, and Actions columns working correctly
- **Import architecture standardized** - Eliminated all @/ alias dependencies, established robust relative import system across entire codebase
- **Portfolio API Data Models Created (January 21, 2025)** - Comprehensive TypeScript data models prepared for real API integration
  - `shared/portfolio-api-models.ts`: Complete API interface definitions based on current mock data analysis
  - Models include: PortfolioAssetApiModel, PortfolioApiModel, PortfolioMetricsApiModel, TransactionApiModel
  - Full CRUD operation interfaces, pagination, error handling, and filtering capabilities defined
  - Ready for backend implementation and frontend mock-to-API transition
- **Portfolio API Integration LIVE (January 21, 2025)** - Real API calls now active on /portfolio-analysis page
  - `client/src/lib/services/PortfolioService.ts`: Updated to use client.php pattern with f="get_portfolios" and f="get_portfolio" 
  - API requests now follow existing application pattern: POST to /client.php with function parameters
  - React Query integration active with proper caching and error handling
  - Fallback to mock data maintained for graceful degradation
  - Console logging shows API request/response flow for debugging
- **Add Asset Endpoint Integration COMPLETED (January 21, 2025)** - Portfolio asset addition now uses real API
  - `addAsset` method updated to use f="add_asset" parameter following client.php pattern
  - `AddAssetRequest` interface enhanced with optional fields: symbol, currentPrice, purchaseDate, exchangeSource
  - FormData structure includes all necessary fields for backend processing
  - TypeScript compatibility fully maintained with proper interface definitions
  - Ready for production use with proper error handling and logging

## System Architecture
The platform utilizes a hybrid architecture, combining a modern React frontend with integration to an existing legacy backend system.

**Frontend Architecture:**
- Built with React 18 and TypeScript, using Vite for development and building.
- Employs a component-based architecture with reusable UI elements, specifically Shadcn/ui components with Radix UI primitives.
- Styling is managed with Tailwind CSS, including custom theme configurations.
- State management relies on React Query (TanStack Query) for server state and Context API for global application state.
- Supports multi-language internationalization with dynamic translation loading and a fallback system.
- Key features include cryptocurrency listing and analysis, AI-powered scoring, real-time data via WebSocket, user authentication, watchlist management, and an alert system.

**Backend Architecture:**
- An Express.js server acts primarily as an API proxy, also hosting a WebSocket server for real-time notifications and serving static files.
- Uses a PostgreSQL database with Drizzle ORM for data persistence, including schemas for users, watchlists, alerts, and a knowledge base.
- Features a badge system for user achievements.
- Integrates with the legacy backend (api.coinscout.app) via a proxy layer and various external cryptocurrency data APIs.

**Data Flow:**
- Client requests from the React frontend are routed through the local Express server.
- The Express server acts as a proxy, forwarding requests to the legacy backend and transforming responses for the frontend.
- Real-time data updates are pushed to the client via WebSocket connections.
- User preferences and session data are cached locally on the client side.

**UI/UX Decisions:**
- Modern, futuristic enterprise design with consistent styling across components.
- Responsive design principles applied for optimal viewing across various devices, including specific mobile responsiveness enhancements for navigation and content display.
- Utilizes Framer Motion for animations and Lottie React Player for animated elements.
- Employs Shadcn/ui and Radix UI for UI components, ensuring a consistent look and feel.
- Color schemes utilize a primary theme color (hsl(201, 90%, 50%)) for visual consistency.
- Icons are sourced from Lucide React.

**Technical Implementations:**
- Robust internationalization system supporting multiple languages, with specific focus on English and Turkish.
- Authentication system includes Google OAuth and a proactive token refresh mechanism via Axios interceptors and WebSocket.
- Comprehensive alert and notification system with WebSocket integration for real-time updates and read tracking.
- Dynamic data handling for features like IDO details, launchpads, and pricing plans, adapting to new API response structures.
- Smart tab disabling in detail pages to grey out sections without meaningful data.
- Comparison functionality with detailed analysis navigation and category-based metrics.

## External Dependencies
- **Third-party APIs:** CoinCap API (cryptocurrency data), Google OAuth (authentication).
- **Development Tools:** Vite (build tool), TypeScript (language), ESLint and Prettier (code quality), Drizzle Kit (database migrations).
- **UI/UX Libraries:** Framer Motion (animations), React Hook Form (form handling), Lucide React (icons), Lottie React Player (animations).
```