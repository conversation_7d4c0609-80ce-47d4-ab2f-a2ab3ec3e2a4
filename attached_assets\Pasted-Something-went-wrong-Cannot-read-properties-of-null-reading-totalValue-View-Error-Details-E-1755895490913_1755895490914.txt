Something went wrong
Cannot read properties of null (reading 'totalValue')

View Error Details
Error Stack
TypeError: Cannot read properties of null (reading 'totalValue') at PortfolioAnalysisOptimized (https://58ce5443-1fac-4e63-b73d-8fbaee5eda89-00-2jx871okpzr37.pike.replit.dev/src/pages/PortfolioAnalysisOptimized.tsx:591:34) at renderWithHooks (https://58ce5443-1fac-4e63-b73d-8fbaee5eda89-00-2jx871okpzr37.pike.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-RPCDYKBN.js?v=67fda76c:11548:26) at updateFunctionComponent (https://58ce5443-1fac-4e63-b73d-8fbaee5eda89-00-2jx871okpzr37.pike.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-RPCDYKBN.js?v=67fda76c:14582:28) at mountLazyComponent (https://58ce5443-1fac-4e63-b73d-8fbaee5eda89-00-2jx871okpzr37.pike.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-RPCDYKBN.js?v=67fda76c:14833:23) at beginWork (https://58ce5443-1fac-4e63-b73d-8fbaee5eda89-00-2jx871okpzr37.pike.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-RPCDYKBN.js?v=67fda76c:15918:22) at beginWork$1 (https://58ce5443-1fac-4e63-b73d-8fbaee5eda89-00-2jx871okpzr37.pike.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-RPCDYKBN.js?v=67fda76c:19753:22) at performUnitOfWork (https://58ce5443-1fac-4e63-b73d-8fbaee5eda89-00-2jx871okpzr37.pike.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-RPCDYKBN.js?v=67fda76c:19198:20) at workLoopSync (https://58ce5443-1fac-4e63-b73d-8fbaee5eda89-00-2jx871okpzr37.pike.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-RPCDYKBN.js?v=67fda76c:19137:13) at renderRootSync (https://58ce5443-1fac-4e63-b73d-8fbaee5eda89-00-2jx871okpzr37.pike.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-RPCDYKBN.js?v=67fda76c:19116:15) at recoverFromConcurrentError (https://58ce5443-1fac-4e63-b73d-8fbaee5eda89-00-2jx871okpzr37.pike.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-RPCDYKBN.js?v=67fda76c:18736:28)

Component Stack
at PortfolioAnalysisOptimized (https://58ce5443-1fac-4e63-b73d-8fbaee5eda89-00-2jx871okpzr37.pike.replit.dev/src/pages/PortfolioAnalysisOptimized.tsx:284:27) at RouteAuthWrapper (https://58ce5443-1fac-4e63-b73d-8fbaee5eda89-00-2jx871okpzr37.pike.replit.dev/src/components/auth/RouteAuthWrapper.tsx:24:3) at Route (https://58ce5443-1fac-4e63-b73d-8fbaee5eda89-00-2jx871okpzr37.pike.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/wouter.js?v=67fda76c:191:16) at Switch (https://58ce5443-1fac-4e63-b73d-8fbaee5eda89-00-2jx871okpzr37.pike.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/wouter.js?v=67fda76c:247:17) at Suspense at ErrorBoundaryInternal (https://58ce5443-1fac-4e63-b73d-8fbaee5eda89-00-2jx871okpzr37.pike.replit.dev/src/components/ErrorBoundary.tsx:33:5) at ErrorBoundary (https://58ce5443-1fac-4e63-b73d-8fbaee5eda89-00-2jx871okpzr37.pike.replit.dev/src/components/ErrorBoundary.tsx:261:17) at div at main at Router (https://58ce5443-1fac-4e63-b73d-8fbaee5eda89-00-2jx871okpzr37.pike.replit.dev/src/App.tsx:200:22) at div at WatchlistProvider (https://58ce5443-1fac-4e63-b73d-8fbaee5eda89-00-2jx871okpzr37.pike.replit.dev/src/components/watchlists/WatchlistProvider.tsx:24:37) at SubscriptionProvider (https://58ce5443-1fac-4e63-b73d-8fbaee5eda89-00-2jx871okpzr37.pike.replit.dev/src/contexts/SubscriptionContext.tsx:34:40) at Provider (https://58ce5443-1fac-4e63-b73d-8fbaee5eda89-00-2jx871okpzr37.pike.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-7X6RRC7I.js?v=67fda76c:38:15) at TooltipProvider (https://58ce5443-1fac-4e63-b73d-8fbaee5eda89-00-2jx871okpzr37.pike.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/@radix-ui_react-tooltip.js?v=67fda76c:902:5) at FilterProvider (https://58ce5443-1fac-4e63-b73d-8fbaee5eda89-00-2jx871okpzr37.pike.replit.dev/src/contexts/FilterContext.tsx:29:34) at SidebarProvider (https://58ce5443-1fac-4e63-b73d-8fbaee5eda89-00-2jx871okpzr37.pike.replit.dev/src/contexts/SidebarContext.tsx:20:35) at AuthProvider (https://58ce5443-1fac-4e63-b73d-8fbaee5eda89-00-2jx871okpzr37.pike.replit.dev/src/hooks/use-auth.tsx:62:32) at LanguageProvider (https://58ce5443-1fac-4e63-b73d-8fbaee5eda89-00-2jx871okpzr37.pike.replit.dev/src/contexts/LanguageContext.tsx:23:36) at QueryClientProvider (https://58ce5443-1fac-4e63-b73d-8fbaee5eda89-00-2jx871okpzr37.pike.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/@tanstack_react-query.js?v=67fda76c:2835:3) at ThemeProvider (https://58ce5443-1fac-4e63-b73d-8fbaee5eda89-00-2jx871okpzr37.pike.replit.dev/src/components/theme-provider.tsx:26:3) at App (https://58ce5443-1fac-4e63-b73d-8fbaee5eda89-00-2jx871okpzr37.pike.replit.dev/src/App.tsx:863:3) at GoogleOAuthProvider (https://58ce5443-1fac-4e63-b73d-8fbaee5eda89-00-2jx871okpzr37.pike.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/@react-oauth_google.js?v=67fda76c:42:32) at QueryClientProvider (https://58ce5443-1fac-4e63-b73d-8fbaee5eda89-00-2jx871okpzr37.pike.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/@tanstack_react-query.js?v=67fda76c:2835:3)